# Better Care Hospital Management - Setup Guide

This guide will help you set up the Better Care hospital management application with the correct database schema and test user accounts.

## 🚀 Quick Setup

### Prerequisites

1. **Supabase Service Key**: You need the service role key from your Supabase project
2. **Node.js**: Ensure Node.js is installed on your system
3. **Project Dependencies**: Run `npm install` to install all dependencies

### Step 1: Get Your Supabase Service Key

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: **vxzpeinztvurtncaaqdm**
3. Go to **Settings** → **API**
4. Copy the **service_role** key (not the anon key)

### Step 2: Run the Setup Script

```bash
# Set your service key as an environment variable
export SUPABASE_SERVICE_KEY="your_service_role_key_here"

# Run the automated setup
npm run setup
```

**Windows (PowerShell):**
```powershell
$env:SUPABASE_SERVICE_KEY="your_service_role_key_here"
npm run setup
```

**Windows (Command Prompt):**
```cmd
set SUPABASE_SERVICE_KEY=your_service_role_key_here
npm run setup
```

## 🔧 What the Setup Script Does

### 1. Database Migration
- Creates the `hospitals` table for multi-tenant support
- Creates the `users` table for application user management
- Adds `hospital_id` columns to existing tables (`patients`, `soins`, `factures`, `facture_items`)
- Creates a default hospital and links existing data
- Sets up database triggers for `updated_at` columns

### 2. Row Level Security (RLS)
- Enables RLS on all tables
- Creates policies for super-admin access (can see all data)
- Creates policies for hospital-admin access (can only see their hospital's data)
- Ensures proper data isolation between hospitals

### 3. Test User Accounts
Creates two test accounts for development and testing:

#### Super Admin Account
- **Email**: `<EMAIL>`
- **Password**: `BetterCare2024!`
- **Role**: `super_admin`
- **Access**: Can manage all hospitals and view all data

#### Hospital Admin Account
- **Email**: `<EMAIL>`
- **Password**: `Hospital2024!`
- **Role**: `hospital_admin`
- **Access**: Can only manage their assigned hospital's data

### 4. Authentication Verification
- Tests login functionality for both accounts
- Verifies proper role assignment
- Ensures database linking between `auth.users` and `public.users`

## 🧪 Testing the Setup

After running the setup script, you can test the application:

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Access the application**: Open http://localhost:8081

3. **Test login with either account**:
   - Super Admin: `<EMAIL>` / `BetterCare2024!`
   - Hospital Admin: `<EMAIL>` / `Hospital2024!`

## 🔍 Troubleshooting

### Common Issues

#### "SUPABASE_SERVICE_KEY is required"
- Make sure you've set the environment variable correctly
- Verify you're using the service_role key, not the anon key

#### "403 Forbidden" or "Access denied"
- Check that your service key is correct
- Ensure you have admin access to the Supabase project

#### "Table already exists" warnings
- These are normal if you've run the script before
- The script handles existing objects gracefully

#### Authentication fails after setup
- Clear your browser's local storage
- Try logging in with the exact credentials provided
- Check the browser console for error messages

### Manual Verification

You can manually verify the setup in the Supabase dashboard:

1. **Database Tables**: Check that `hospitals` and `users` tables exist
2. **Authentication**: Go to Auth → Users to see the created accounts
3. **RLS Policies**: Check that policies are enabled on all tables

## 🔄 Re-running the Setup

The setup script is idempotent, meaning you can run it multiple times safely:
- Existing database objects won't be duplicated
- Existing users won't be recreated
- The script will update any missing configurations

## 📝 Manual Setup (Alternative)

If the automated script doesn't work, you can manually:

1. **Apply migrations**: Copy and paste the SQL from `supabase/migrations/` into the Supabase SQL Editor
2. **Create users**: Use the Supabase Auth interface to create user accounts
3. **Link users**: Manually insert records into the `public.users` table

## 🔐 Security Notes

- **Change passwords in production**: The provided passwords are for development only
- **Service key security**: Never commit your service key to version control
- **RLS policies**: The setup includes proper row-level security for data isolation

## 📞 Support

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify your Supabase project configuration
3. Ensure all prerequisites are met
4. Try the manual setup approach if needed

---

**Next Steps**: After successful setup, you can start developing and testing the Better Care hospital management features!
