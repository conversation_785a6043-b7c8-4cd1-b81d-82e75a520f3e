# Better Care - Troubleshooting Guide

## 🚨 "Invalid API key" Error

### Problem
You're seeing this error when trying to log in:
```
AuthApiError: Invalid API key
```

### Cause
The Supabase API keys in the application don't match your actual Supabase project.

### Quick Fix

#### Option 1: Automated Fix (Recommended)
```bash
npm run fix-keys
```
This will guide you through updating the API keys interactively.

#### Option 2: Manual Fix

1. **Get your correct API keys:**
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Select your project: `vxzpeinztvurtncaaqdm`
   - Go to **Settings** → **API**
   - Copy the **anon/public** key

2. **Update the client configuration:**
   Edit `src/integrations/supabase/client.ts`:
   ```typescript
   const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
   const SUPABASE_PUBLISHABLE_KEY = "your_actual_anon_key_here";
   ```

3. **Restart the development server:**
   ```bash
   npm run dev
   ```

4. **Clear browser storage:**
   - Open Developer Tools (F12)
   - Go to Application/Storage tab
   - Clear Local Storage and Session Storage

## 🔐 Authentication Issues

### "User not found" Error
This means the test accounts haven't been created yet.

**Solution:**
```bash
# Get your service key from Supabase Dashboard → Settings → API → service_role
export SUPABASE_SERVICE_KEY="your_service_role_key"
npm run setup
```

### "Access denied" or "Unauthorized" Error
This usually means RLS policies aren't set up correctly.

**Solution:**
1. Run the setup script to apply RLS policies
2. Verify the setup: `npm run verify`

## 🗄️ Database Issues

### "Table does not exist" Error
The database migration hasn't been applied.

**Solution:**
```bash
export SUPABASE_SERVICE_KEY="your_service_role_key"
npm run setup
```

### "Permission denied" Error
RLS policies are blocking access.

**Solution:**
1. Check that you're logged in with the correct account
2. Verify RLS policies are applied: `npm run setup`
3. Check user roles in the database

## 🔧 Setup Script Issues

### "SUPABASE_SERVICE_KEY is required"
You need to set the service role key as an environment variable.

**Solution:**
```bash
# Get service key from Supabase Dashboard → Settings → API → service_role
export SUPABASE_SERVICE_KEY="your_service_role_key_here"
npm run setup
```

### "403 Forbidden" Error
Your service key doesn't have the right permissions.

**Solution:**
1. Make sure you're using the **service_role** key, not the anon key
2. Verify you have admin access to the Supabase project
3. Check that the project ID is correct

## 🌐 Network Issues

### "Failed to fetch" Error
Network connectivity or CORS issues.

**Solution:**
1. Check your internet connection
2. Verify the Supabase URL is correct
3. Check if your firewall is blocking the connection

### Timeout Errors
The Supabase service might be slow or unavailable.

**Solution:**
1. Check [Supabase Status](https://status.supabase.com/)
2. Try again in a few minutes
3. Verify your project is active in the dashboard

## 🧪 Testing Issues

### Login Works But No Data Shows
RLS policies might be too restrictive.

**Solution:**
1. Check that the user has the correct role
2. Verify the user is linked to a hospital (for hospital_admin)
3. Run: `npm run verify` to check the setup

### "Hospital not found" Error
The default hospital wasn't created.

**Solution:**
```bash
npm run setup  # This will create the default hospital
```

## 🔍 Debugging Steps

### 1. Check Current Configuration
```bash
# View current Supabase configuration
cat src/integrations/supabase/client.ts
cat supabase/config.toml
```

### 2. Verify API Keys
```bash
# Test if your keys work
npm run verify
```

### 3. Check Database State
Log into your Supabase dashboard and verify:
- Tables exist: `hospitals`, `users`, `patients`, etc.
- Test users exist in Authentication → Users
- RLS policies are enabled on tables

### 4. Check Browser Console
Open Developer Tools (F12) and check for:
- Network errors in the Network tab
- JavaScript errors in the Console tab
- Authentication state in Application → Local Storage

## 📞 Getting Help

### Before Asking for Help
1. Run the verification script: `npm run verify`
2. Check the browser console for errors
3. Verify your Supabase project is active
4. Try the automated fix: `npm run fix-keys`

### Information to Provide
- Error message (full stack trace)
- Browser console output
- Results of `npm run verify`
- Your Supabase project URL
- Steps you've already tried

## 🔄 Complete Reset

If nothing else works, you can start fresh:

1. **Reset the database:**
   - Go to Supabase Dashboard → Settings → General
   - Reset your project (this will delete all data)

2. **Run the setup again:**
   ```bash
   npm run fix-keys  # Update API keys
   npm run setup     # Apply migrations and create users
   npm run verify    # Confirm everything works
   ```

3. **Clear browser data:**
   - Clear all browser storage
   - Restart the development server

## ✅ Success Indicators

You know everything is working when:
- ✅ `npm run verify` passes all checks
- ✅ You can log in with both test accounts
- ✅ The dashboard loads without errors
- ✅ You can see hospital and patient data
- ✅ No errors in the browser console

---

**Remember:** Most issues are caused by incorrect API keys or incomplete setup. The automated tools (`npm run fix-keys` and `npm run setup`) solve 90% of problems!
