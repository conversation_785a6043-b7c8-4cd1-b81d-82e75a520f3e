#!/usr/bin/env node

/**
 * <PERSON>ript to create auth users in Supabase
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9zaGxocmdhenRmYmlodXhlb3B1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjQzNzU4MiwiZXhwIjoyMDYyMDEzNTgyfQ.UAgFMoZDmfwg1GpRjfSZj4lrh72x7RlB8Eq_LtTcjhs";

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'BetterCare2024!',
    role: 'super_admin',
    full_name: 'Super Administrateur'
  },
  {
    email: '<EMAIL>',
    password: 'Hospital2024!',
    role: 'hospital_admin',
    full_name: 'Administrateur Hôpital'
  }
];

async function createAuthUsers() {
  console.log('🔐 Creating auth users...');

  for (const user of TEST_USERS) {
    try {
      console.log(`Creating user: ${user.email}`);
      
      const { data, error } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true
      });

      if (error) {
        if (error.message.includes('already registered')) {
          console.log(`✅ User ${user.email} already exists`);
        } else {
          console.error(`❌ Failed to create ${user.email}:`, error.message);
        }
      } else {
        console.log(`✅ Created auth user: ${user.email}`);
      }

    } catch (error) {
      console.error(`❌ Error creating ${user.email}:`, error.message);
    }
  }
}

async function verifyUsers() {
  console.log('\n🔍 Verifying users...');
  
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('*');

    if (error) {
      console.error('❌ Error fetching users:', error.message);
      return;
    }

    console.log(`✅ Found ${users.length} users in public.users table:`);
    users.forEach(user => {
      console.log(`  - ${user.email} (${user.role})`);
    });

  } catch (error) {
    console.error('❌ Error verifying users:', error.message);
  }
}

async function main() {
  try {
    await createAuthUsers();
    await verifyUsers();
    
    console.log('\n🎉 Setup completed!');
    console.log('\nTest Accounts:');
    TEST_USERS.forEach(user => {
      console.log(`📧 ${user.email}`);
      console.log(`🔑 ${user.password}`);
      console.log(`👤 ${user.role}\n`);
    });
    
    console.log('Next steps:');
    console.log('1. Restart your dev server: npm run dev');
    console.log('2. Clear browser storage');
    console.log('3. Try logging in with the test accounts');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main();
