// <PERSON>ript to delete all data from Supabase tables
import { createClient } from '@supabase/supabase-js';

// Supabase connection details
const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9zaGxocmdhenRmYmlodXhlb3B1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0Mzc1ODIsImV4cCI6MjA2MjAxMzU4Mn0.ouZSe00DWjc0bSjO7FfLwOdoIlgDAyD7HErUjYjwB8o";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Function to delete all data from a table
async function deleteAllFromTable(tableName) {
  console.log(`Deleting all data from ${tableName}...`);
  const { error } = await supabase
    .from(tableName)
    .delete()
    .neq('id', 'no-match'); // This is a trick to delete all rows

  if (error) {
    console.error(`Error deleting data from ${tableName}:`, error);
    return false;
  }

  console.log(`Successfully deleted all data from ${tableName}`);
  return true;
}

// Main function to delete all data in the correct order
async function deleteAllData() {
  try {
    console.log("Starting to delete all data from Supabase tables...");

    // Delete in order to respect foreign key constraints
    await deleteAllFromTable('facture_items');
    await deleteAllFromTable('factures');
    await deleteAllFromTable('soins');
    await deleteAllFromTable('patients');

    console.log("All data has been successfully deleted from all tables!");
  } catch (error) {
    console.error("An error occurred while deleting data:", error);
  }
}

// Run the deletion process
deleteAllData();
