#!/usr/bin/env node

/**
 * Better Care - API Key Configuration Fix
 * 
 * This script helps you update the Supabase API keys to fix authentication issues
 */

import fs from 'fs';
import readline from 'readline';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

class APIKeyFixer {
  constructor() {
    this.projectId = 'vxzpeinztvurtncaaqdm';
    this.projectUrl = `https://${this.projectId}.supabase.co`;
  }

  async run() {
    try {
      log('\n🔧 Better Care - API Key Configuration Fix', 'magenta');
      log('=' * 60, 'magenta');

      logError('Authentication Error Detected: Invalid API key');
      log('\nThis error occurs when the Supabase API keys don\'t match the project.', 'yellow');
      log('Let\'s fix this by updating the configuration with the correct keys.\n', 'yellow');

      await this.getCorrectAPIKeys();
      await this.updateConfiguration();
      await this.showNextSteps();

    } catch (error) {
      logError(`Fix failed: ${error.message}`);
      process.exit(1);
    } finally {
      rl.close();
    }
  }

  async getCorrectAPIKeys() {
    log('📋 Getting Correct API Keys', 'cyan');
    log('=' * 40, 'cyan');

    logInfo('Please follow these steps to get your API keys:');
    log('\n1. Go to: https://supabase.com/dashboard', 'blue');
    log(`2. Select your project: ${this.projectId}`, 'blue');
    log('3. Go to Settings → API', 'blue');
    log('4. Copy the keys as requested below\n', 'blue');

    // Get Project URL
    const projectUrl = await askQuestion(`Enter your project URL (default: ${this.projectUrl}): `);
    this.projectUrl = projectUrl || this.projectUrl;

    // Get anon key
    log('\n🔑 Anon Key (Public Key)', 'cyan');
    logInfo('This is the "anon" or "public" key from your Supabase project settings.');
    const anonKey = await askQuestion('Enter your anon/public key: ');

    if (!anonKey) {
      throw new Error('Anon key is required');
    }

    // Get service key (optional, for setup script)
    log('\n🔑 Service Key (Optional)', 'cyan');
    logInfo('This is the "service_role" key - only needed if you plan to run the setup script.');
    const serviceKey = await askQuestion('Enter your service key (optional, press Enter to skip): ');

    this.keys = {
      projectUrl: this.projectUrl,
      anonKey: anonKey,
      serviceKey: serviceKey || null
    };

    logSuccess('API keys collected successfully!');
  }

  async updateConfiguration() {
    log('\n🔄 Updating Configuration Files', 'cyan');
    log('=' * 40, 'cyan');

    try {
      // Update Supabase client configuration
      await this.updateSupabaseClient();
      
      // Update verification script
      await this.updateVerificationScript();
      
      // Update config.toml
      await this.updateConfigToml();

      logSuccess('All configuration files updated successfully!');

    } catch (error) {
      logError(`Configuration update failed: ${error.message}`);
      throw error;
    }
  }

  async updateSupabaseClient() {
    const clientPath = 'src/integrations/supabase/client.ts';
    
    try {
      const content = `
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "${this.keys.projectUrl}";
const SUPABASE_PUBLISHABLE_KEY = "${this.keys.anonKey}";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
`;

      fs.writeFileSync(clientPath, content.trim() + '\n');
      logSuccess(`Updated ${clientPath}`);

    } catch (error) {
      logError(`Failed to update ${clientPath}: ${error.message}`);
      throw error;
    }
  }

  async updateVerificationScript() {
    const verifyPath = 'verify-setup.js';
    
    try {
      let content = fs.readFileSync(verifyPath, 'utf8');
      
      // Update URL
      content = content.replace(
        /const SUPABASE_URL = ".*";/,
        `const SUPABASE_URL = "${this.keys.projectUrl}";`
      );
      
      // Update anon key
      content = content.replace(
        /const SUPABASE_ANON_KEY = ".*";/,
        `const SUPABASE_ANON_KEY = "${this.keys.anonKey}";`
      );

      fs.writeFileSync(verifyPath, content);
      logSuccess(`Updated ${verifyPath}`);

    } catch (error) {
      logWarning(`Could not update ${verifyPath}: ${error.message}`);
    }
  }

  async updateConfigToml() {
    const configPath = 'supabase/config.toml';
    
    try {
      const projectId = this.keys.projectUrl.match(/https:\/\/(.+)\.supabase\.co/)?.[1];
      if (projectId) {
        fs.writeFileSync(configPath, `project_id = "${projectId}"\n`);
        logSuccess(`Updated ${configPath}`);
      }
    } catch (error) {
      logWarning(`Could not update ${configPath}: ${error.message}`);
    }
  }

  async showNextSteps() {
    log('\n🎉 Configuration Updated Successfully!', 'green');
    log('=' * 50, 'green');

    logInfo('Next steps:');
    log('\n1. Restart your development server:', 'blue');
    log('   npm run dev', 'cyan');
    
    log('\n2. Clear your browser cache and local storage:', 'blue');
    log('   - Open Developer Tools (F12)', 'cyan');
    log('   - Go to Application/Storage tab', 'cyan');
    log('   - Clear Local Storage and Session Storage', 'cyan');
    
    log('\n3. Try logging in again with the test accounts:', 'blue');
    log('   - Super Admin: <EMAIL> / BetterCare2024!', 'cyan');
    log('   - Hospital Admin: <EMAIL> / Hospital2024!', 'cyan');

    if (this.keys.serviceKey) {
      log('\n4. If you haven\'t run the setup yet, run:', 'blue');
      log(`   export SUPABASE_SERVICE_KEY="${this.keys.serviceKey}"`, 'cyan');
      log('   npm run setup', 'cyan');
    } else {
      log('\n4. To run the setup script later, you\'ll need the service key:', 'blue');
      log('   Get it from Supabase Dashboard → Settings → API → service_role', 'cyan');
    }

    log('\n5. Verify everything works:', 'blue');
    log('   npm run verify', 'cyan');

    log('\n📝 Configuration Summary:', 'magenta');
    log(`   Project URL: ${this.keys.projectUrl}`, 'blue');
    log(`   Anon Key: ${this.keys.anonKey.substring(0, 20)}...`, 'blue');
    log(`   Service Key: ${this.keys.serviceKey ? 'Provided' : 'Not provided'}`, 'blue');
  }
}

// Run the fixer
const fixer = new APIKeyFixer();
fixer.run();
