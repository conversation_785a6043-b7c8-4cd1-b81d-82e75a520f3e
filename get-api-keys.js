#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to help you get and test the correct Supabase API keys
 */

import { createClient } from '@supabase/supabase-js';
import readline from 'readline';

const PROJECT_ID = 'vxzpeinztvurtncaaqdm';
const PROJECT_URL = `https://${PROJECT_ID}.supabase.co`;

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function testApiKey(anonKey) {
  try {
    log('\n🧪 Testing API key...', 'cyan');
    
    const supabase = createClient(PROJECT_URL, anonKey);
    
    // Test basic connection
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      log(`❌ API key test failed: ${error.message}`, 'red');
      return false;
    }
    
    log('✅ API key is valid!', 'green');
    
    // Test database access
    try {
      const { data: testData, error: dbError } = await supabase
        .from('hospitals')
        .select('count')
        .limit(1);
        
      if (dbError) {
        log(`⚠️  API key works but database access failed: ${dbError.message}`, 'yellow');
        log('   This might be normal if tables don\'t exist yet.', 'yellow');
      } else {
        log('✅ Database access confirmed!', 'green');
      }
    } catch (dbError) {
      log(`⚠️  Database test failed: ${dbError.message}`, 'yellow');
    }
    
    return true;
  } catch (error) {
    log(`❌ API key test failed: ${error.message}`, 'red');
    return false;
  }
}

async function updateConfiguration(anonKey) {
  try {
    const fs = await import('fs');
    
    // Update client.ts
    const clientContent = `
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "${PROJECT_URL}";
const SUPABASE_PUBLISHABLE_KEY = "${anonKey}";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
`.trim();

    fs.writeFileSync('src/integrations/supabase/client.ts', clientContent + '\n');
    log('✅ Updated src/integrations/supabase/client.ts', 'green');
    
    // Update verify-setup.js if it exists
    try {
      let verifyContent = fs.readFileSync('verify-setup.js', 'utf8');
      verifyContent = verifyContent.replace(
        /const SUPABASE_ANON_KEY = ".*";/,
        `const SUPABASE_ANON_KEY = "${anonKey}";`
      );
      fs.writeFileSync('verify-setup.js', verifyContent);
      log('✅ Updated verify-setup.js', 'green');
    } catch (error) {
      log('⚠️  Could not update verify-setup.js', 'yellow');
    }
    
    return true;
  } catch (error) {
    log(`❌ Failed to update configuration: ${error.message}`, 'red');
    return false;
  }
}

async function main() {
  try {
    log('\n🔑 Supabase API Key Helper', 'magenta');
    log('=' * 40, 'magenta');
    
    log(`\nProject: ${PROJECT_ID}`, 'blue');
    log(`URL: ${PROJECT_URL}`, 'blue');
    
    log('\n📋 To get your API keys:', 'cyan');
    log('1. Go to: https://supabase.com/dashboard', 'blue');
    log(`2. Select your project: ${PROJECT_ID}`, 'blue');
    log('3. Go to Settings → API', 'blue');
    log('4. Copy the "anon" or "public" key (NOT the service_role key)', 'blue');
    
    const anonKey = await askQuestion('\n🔑 Enter your anon/public key: ');
    
    if (!anonKey) {
      log('❌ No key provided', 'red');
      process.exit(1);
    }
    
    if (!anonKey.startsWith('eyJ')) {
      log('❌ Invalid key format. The anon key should start with "eyJ"', 'red');
      process.exit(1);
    }
    
    const isValid = await testApiKey(anonKey);
    
    if (isValid) {
      const shouldUpdate = await askQuestion('\n💾 Update configuration files? (y/n): ');
      
      if (shouldUpdate.toLowerCase() === 'y' || shouldUpdate.toLowerCase() === 'yes') {
        const updated = await updateConfiguration(anonKey);
        
        if (updated) {
          log('\n🎉 Configuration updated successfully!', 'green');
          log('\nNext steps:', 'cyan');
          log('1. Restart your dev server: npm run dev', 'blue');
          log('2. Clear browser storage (F12 → Application → Clear Storage)', 'blue');
          log('3. Try logging in again', 'blue');
          
          log('\nIf you need to create test accounts:', 'cyan');
          log('1. Get your service_role key from Supabase Dashboard', 'blue');
          log('2. export SUPABASE_SERVICE_KEY="your_service_key"', 'blue');
          log('3. npm run setup', 'blue');
        }
      } else {
        log('\n📝 Your anon key is valid:', 'green');
        log(`${anonKey}`, 'cyan');
        log('\nManually update src/integrations/supabase/client.ts with this key.', 'blue');
      }
    } else {
      log('\n❌ The provided API key is not valid.', 'red');
      log('Please check that you copied the correct "anon" key from your Supabase dashboard.', 'yellow');
    }
    
  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
  } finally {
    rl.close();
  }
}

main();
