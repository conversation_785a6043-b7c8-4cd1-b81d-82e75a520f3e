#!/usr/bin/env node

/**
 * Better Care Hospital Management - Automated Setup Script
 * 
 * This script performs the following tasks:
 * 1. Applies multi-tenant database migration
 * 2. Creates test user accounts in Supabase Auth
 * 3. Links users between auth.users and public.users
 * 4. Verifies authentication system
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Configuration
const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.UAgFMoZDmfwg1GpRjfSZj4lrh72x7RlB8Eq_LtTcjhs";
const PROJECT_ID = "oshlhrgaztfbihuxeopu";

// Test user credentials
const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'BetterCare2024!',
    role: 'super_admin',
    full_name: 'Super Administrateur',
    hospital_id: null
  },
  {
    email: '<EMAIL>',
    password: 'Hospital2024!',
    role: 'hospital_admin',
    full_name: 'Administrateur Hôpital',
    hospital_id: '********-0000-0000-0000-************'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

class BetterCareSetup {
  constructor() {
    if (!SUPABASE_SERVICE_KEY) {
      logError('SUPABASE_SERVICE_KEY environment variable is required');
      logError('Please set it with: export SUPABASE_SERVICE_KEY="your_service_key"');
      process.exit(1);
    }

    this.supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
  }

  async run() {
    try {
      log('\n🏥 Better Care Hospital Management - Setup Script', 'magenta');
      log('=' * 60, 'magenta');

      await this.step1_ApplyMigrations();
      await this.step2_CreateTestUsers();
      await this.step3_LinkUsers();
      await this.step4_VerifySetup();

      log('\n🎉 Setup completed successfully!', 'green');
      log('\nTest Accounts Created:', 'cyan');
      TEST_USERS.forEach(user => {
        log(`  📧 ${user.email} (${user.role})`, 'blue');
        log(`  🔑 Password: ${user.password}`, 'blue');
      });

    } catch (error) {
      logError(`Setup failed: ${error.message}`);
      process.exit(1);
    }
  }

  async step1_ApplyMigrations() {
    logStep('1', 'Applying database migrations...');

    try {
      // Read migration files
      const migration1 = fs.readFileSync('supabase/migrations/001_multi_tenant_setup.sql', 'utf8');
      const migration2 = fs.readFileSync('supabase/migrations/002_rls_policies.sql', 'utf8');

      // Apply first migration
      logStep('1.1', 'Applying multi-tenant setup migration...');
      await this.executeSQLMigration(migration1);
      logSuccess('Multi-tenant setup migration applied');

      // Apply second migration
      logStep('1.2', 'Applying RLS policies migration...');
      await this.executeSQLMigration(migration2);
      logSuccess('RLS policies migration applied');

    } catch (error) {
      if (error.message.includes('already exists')) {
        logWarning('Some database objects already exist - continuing...');
      } else {
        throw error;
      }
    }
  }

  async executeSQLMigration(sql) {
    // Use Supabase Management API to execute SQL
    try {
      const response = await fetch(`https://api.supabase.com/v1/projects/${PROJECT_ID}/database/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`
        },
        body: JSON.stringify({ query: sql })
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (errorData.message && errorData.message.includes('already exists')) {
          logWarning('Some database objects already exist - continuing...');
          return;
        }
        throw new Error(`SQL execution failed: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      if (error.message.includes('already exists')) {
        logWarning('Database object already exists - continuing...');
        return;
      }
      throw error;
    }
  }

  async step2_CreateTestUsers() {
    logStep('2', 'Creating test user accounts in Supabase Auth...');

    for (const user of TEST_USERS) {
      try {
        logStep('2.1', `Creating auth user: ${user.email}`);

        const { data, error } = await this.supabase.auth.admin.createUser({
          email: user.email,
          password: user.password,
          email_confirm: true
        });

        if (error) {
          if (error.message.includes('already registered')) {
            logWarning(`User ${user.email} already exists in auth`);
          } else {
            throw error;
          }
        } else {
          logSuccess(`Created auth user: ${user.email}`);
        }

      } catch (error) {
        logError(`Failed to create auth user ${user.email}: ${error.message}`);
        throw error;
      }
    }
  }

  async step3_LinkUsers() {
    logStep('3', 'Linking users between auth.users and public.users...');

    for (const user of TEST_USERS) {
      try {
        logStep('3.1', `Linking user: ${user.email}`);

        // Insert or update user in public.users table
        const { error } = await this.supabase
          .from('users')
          .upsert({
            email: user.email,
            role: user.role,
            full_name: user.full_name,
            hospital_id: user.hospital_id
          }, {
            onConflict: 'email'
          });

        if (error) throw error;
        logSuccess(`Linked user: ${user.email} with role ${user.role}`);

      } catch (error) {
        logError(`Failed to link user ${user.email}: ${error.message}`);
        throw error;
      }
    }
  }

  async step4_VerifySetup() {
    logStep('4', 'Verifying setup...');

    try {
      // Check if hospitals table exists and has data
      const { data: hospitals, error: hospitalsError } = await this.supabase
        .from('hospitals')
        .select('*')
        .limit(1);

      if (hospitalsError) throw hospitalsError;
      logSuccess(`Hospitals table verified (${hospitals.length} records)`);

      // Check if users are properly linked
      const { data: users, error: usersError } = await this.supabase
        .from('users')
        .select('*');

      if (usersError) throw usersError;
      logSuccess(`Users table verified (${users.length} records)`);

      // Test authentication for each user
      for (const user of TEST_USERS) {
        const { data, error } = await this.supabase.auth.signInWithPassword({
          email: user.email,
          password: user.password
        });

        if (error) {
          logError(`Authentication test failed for ${user.email}: ${error.message}`);
        } else {
          logSuccess(`Authentication test passed for ${user.email}`);
          // Sign out to clean up
          await this.supabase.auth.signOut();
        }
      }

    } catch (error) {
      logError(`Verification failed: ${error.message}`);
      throw error;
    }
  }
}

// Run the setup
const setup = new BetterCareSetup();
setup.run();
