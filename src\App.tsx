﻿import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Layout from '@/components/layout/Layout';

// Import pages
import Login from '@/pages/Login';
import Dashboard from '@/pages/Dashboard';
import Patients from '@/pages/Patients';
import PatientDetails from '@/pages/PatientDetails';
import Soins from '@/pages/Soins';
import Factures from '@/pages/Factures';
import CreateFacture from '@/pages/CreateFacture';
import EditFacture from '@/pages/EditFacture';
import HospitalProfile from '@/pages/HospitalProfile';
import HospitalList from '@/pages/HospitalList';
import HospitalDetails from '@/pages/HospitalDetails';
import CreateHospital from '@/pages/CreateHospital';
import EditHospital from '@/pages/EditHospital';
import SuperAdminDashboard from '@/pages/SuperAdminDashboard';
import NotFound from '@/pages/NotFound';
import Unauthorized from '@/pages/Unauthorized';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Router>
            <div className="App">
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/unauthorized" element={<Unauthorized />} />
                
                {/* Protected routes with layout */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/patients" element={
                  <ProtectedRoute>
                    <Layout>
                      <Patients />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/patients/:id" element={
                  <ProtectedRoute>
                    <Layout>
                      <PatientDetails />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/soins" element={
                  <ProtectedRoute>
                    <Layout>
                      <Soins />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/factures" element={
                  <ProtectedRoute>
                    <Layout>
                      <Factures />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/create-facture" element={
                  <ProtectedRoute>
                    <Layout>
                      <CreateFacture />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/factures/:id/edit" element={
                  <ProtectedRoute>
                    <Layout>
                      <EditFacture />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/hospital-profile" element={
                  <ProtectedRoute>
                    <Layout>
                      <HospitalProfile />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                {/* Super Admin routes */}
                <Route path="/super-admin" element={
                  <ProtectedRoute requiredRole="super_admin">
                    <Layout>
                      <SuperAdminDashboard />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/super-admin/hospitals" element={
                  <ProtectedRoute requiredRole="super_admin">
                    <Layout>
                      <HospitalList />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/super-admin/hospitals/new" element={
                  <ProtectedRoute requiredRole="super_admin">
                    <Layout>
                      <CreateHospital />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/super-admin/hospitals/:id" element={
                  <ProtectedRoute requiredRole="super_admin">
                    <Layout>
                      <HospitalDetails />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                <Route path="/super-admin/hospitals/:id/edit" element={
                  <ProtectedRoute requiredRole="super_admin">
                    <Layout>
                      <EditHospital />
                    </Layout>
                  </ProtectedRoute>
                } />
                
                {/* Catch all route for 404 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
              
              {/* Toast notifications */}
              <Toaster />
              <SonnerToaster />
            </div>
          </Router>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
