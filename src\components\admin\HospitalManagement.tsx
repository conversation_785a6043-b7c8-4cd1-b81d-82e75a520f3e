import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  Building2, 
  Plus, 
  Eye, 
  UserX, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Users,
  Calendar
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface Hospital {
  id: string;
  name: string;
  contact_email: string;
  contact_phone: string;
  address: string;
  subscription_status: 'active' | 'inactive' | 'suspended';
  account_status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

interface HospitalStats {
  totalHospitals: number;
  activeHospitals: number;
  inactiveHospitals: number;
  suspendedHospitals: number;
}

const HospitalCard = ({ 
  hospital, 
  onStatusChange, 
  onDelete 
}: { 
  hospital: Hospital;
  onStatusChange: (id: string, status: 'active' | 'inactive' | 'suspended') => void;
  onDelete: (id: string) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'inactive': return <XCircle className="h-4 w-4" />;
      case 'suspended': return <UserX className="h-4 w-4" />;
      default: return <XCircle className="h-4 w-4" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <Building2 className="h-5 w-5 mr-2 text-primary" />
            {hospital.name}
          </CardTitle>
          <Badge className={getStatusColor(hospital.subscription_status)}>
            {getStatusIcon(hospital.subscription_status)}
            <span className="ml-1 capitalize">{hospital.subscription_status}</span>
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 mb-4">
          <p className="text-sm text-muted-foreground">
            <strong>Email:</strong> {hospital.contact_email || 'Non renseigné'}
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Téléphone:</strong> {hospital.contact_phone || 'Non renseigné'}
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Adresse:</strong> {hospital.address || 'Non renseignée'}
          </p>
          <p className="text-sm text-muted-foreground flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            Créé le {new Date(hospital.created_at).toLocaleDateString('fr-FR')}
          </p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {/* TODO: Implement view details */}}
          >
            <Eye className="h-4 w-4 mr-1" />
            Voir
          </Button>

          {hospital.subscription_status === 'active' ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(hospital.id, 'suspended')}
                className="text-orange-600 hover:text-orange-700"
              >
                <UserX className="h-4 w-4 mr-1" />
                Suspendre
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(hospital.id, 'inactive')}
                className="text-gray-600 hover:text-gray-700"
              >
                <XCircle className="h-4 w-4 mr-1" />
                Désactiver
              </Button>
            </>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onStatusChange(hospital.id, 'active')}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Activer
            </Button>
          )}

          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDelete(hospital.id)}
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Supprimer
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const StatCard = ({ 
  title, 
  value, 
  icon, 
  className 
}: {
  title: string;
  value: number;
  icon: React.ReactNode;
  className?: string;
}) => (
  <Card className={className}>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
    </CardContent>
  </Card>
);

export default function HospitalManagement() {
  const { toast } = useToast();
  const [hospitals, setHospitals] = useState<Hospital[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<HospitalStats>({
    totalHospitals: 0,
    activeHospitals: 0,
    inactiveHospitals: 0,
    suspendedHospitals: 0
  });

  const loadHospitals = async () => {
    try {
      const { data, error } = await supabase
        .from('hospitals')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setHospitals(data || []);
      
      // Calculate stats
      const totalHospitals = data?.length || 0;
      const activeHospitals = data?.filter(h => h.subscription_status === 'active').length || 0;
      const inactiveHospitals = data?.filter(h => h.subscription_status === 'inactive').length || 0;
      const suspendedHospitals = data?.filter(h => h.subscription_status === 'suspended').length || 0;

      setStats({
        totalHospitals,
        activeHospitals,
        inactiveHospitals,
        suspendedHospitals
      });

    } catch (error) {
      console.error('Error loading hospitals:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger la liste des hôpitaux',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (hospitalId: string, newStatus: 'active' | 'inactive' | 'suspended') => {
    try {
      const { error } = await supabase
        .from('hospitals')
        .update({ 
          subscription_status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', hospitalId);

      if (error) throw error;

      toast({
        title: 'Statut mis à jour',
        description: `Le statut de l'hôpital a été changé vers "${newStatus}"`,
      });

      // Reload hospitals
      loadHospitals();

    } catch (error) {
      console.error('Error updating hospital status:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de mettre à jour le statut de l\'hôpital',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (hospitalId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet hôpital ? Cette action est irréversible.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('hospitals')
        .delete()
        .eq('id', hospitalId);

      if (error) throw error;

      toast({
        title: 'Hôpital supprimé',
        description: 'L\'hôpital a été supprimé avec succès',
      });

      // Reload hospitals
      loadHospitals();

    } catch (error) {
      console.error('Error deleting hospital:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de supprimer l\'hôpital',
        variant: 'destructive'
      });
    }
  };

  useEffect(() => {
    loadHospitals();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Chargement des hôpitaux...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Gestion des Hôpitaux</h2>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Nouvel Hôpital
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard
          title="Total Hôpitaux"
          value={stats.totalHospitals}
          icon={<Building2 className="h-4 w-4 text-muted-foreground" />}
        />
        <StatCard
          title="Actifs"
          value={stats.activeHospitals}
          icon={<CheckCircle className="h-4 w-4 text-green-600" />}
          className="border-green-200"
        />
        <StatCard
          title="Inactifs"
          value={stats.inactiveHospitals}
          icon={<XCircle className="h-4 w-4 text-gray-600" />}
          className="border-gray-200"
        />
        <StatCard
          title="Suspendus"
          value={stats.suspendedHospitals}
          icon={<UserX className="h-4 w-4 text-red-600" />}
          className="border-red-200"
        />
      </div>

      {/* Hospitals Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {hospitals.map((hospital) => (
          <HospitalCard
            key={hospital.id}
            hospital={hospital}
            onStatusChange={handleStatusChange}
            onDelete={handleDelete}
          />
        ))}
      </div>

      {hospitals.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Aucun hôpital trouvé</h3>
            <p className="text-muted-foreground text-center mb-4">
              Commencez par ajouter votre premier hôpital au système.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Ajouter un Hôpital
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
