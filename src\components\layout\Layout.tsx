
import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const isMobile = useIsMobile();
  
  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      <main className={cn(
        "transition-all duration-300 min-h-screen",
        isMobile ? "ml-0 p-4 pt-16" : "ml-20 p-6",
        "lg:ml-64"
      )}>
        {children}
      </main>
    </div>
  );
}
