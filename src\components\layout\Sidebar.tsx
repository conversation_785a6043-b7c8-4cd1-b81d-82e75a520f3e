
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import {
  DollarSign,
  FilePlus,
  FileText,
  Home,
  Menu,
  Users,
  X
} from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useLocation } from "react-router-dom";

interface NavItemProps {
  to: string;
  label: string;
  icon: React.ReactNode;
  isActive: boolean;
  onClick?: () => void;
}

const NavItem = ({ to, label, icon, isActive, onClick }: NavItemProps) => (
  <Link to={to} onClick={onClick}>
    <Button
      variant="ghost"
      className={cn(
        "w-full justify-start mb-1",
        isActive ? "bg-primary/10 text-primary" : ""
      )}
    >
      {icon}
      <span className="ml-2">{label}</span>
    </Button>
  </Link>
);

export default function Sidebar() {
  const location = useLocation();
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(!isMobile);

  useEffect(() => {
    if (isMobile) {
      setIsOpen(false);
    } else {
      setIsOpen(true);
    }
  }, [isMobile]);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  // Navigation items based on user role
  const getNavItems = () => {
    return [
      { to: "/", label: "Tableau de bord", icon: <Home size={20} /> },
      { to: "/patients", label: "Patients", icon: <Users size={20} /> },
      { to: "/soins", label: "Soins & Articles", icon: <FileText size={20} /> },
      { to: "/factures", label: "Factures", icon: <DollarSign size={20} /> },
      { to: "/create-facture", label: "Nouvelle facture", icon: <FilePlus size={20} /> },
      { to: "/hospital-profile", label: "Profil Hôpital", icon: <Building2 size={20} /> },
    ];
  };

  const navItems = getNavItems();

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        onClick={toggleSidebar}
        className="fixed top-4 left-4 z-50 lg:hidden"
      >
        {isOpen ? <X size={20} /> : <Menu size={20} />}
      </Button>

      <div className={cn(
        "fixed top-0 left-0 h-full bg-white shadow-md transition-all duration-300 z-40",
        isOpen ? "w-64" : (isMobile ? "w-0 -translate-x-full" : "w-20"),
      )}>
        <div className="p-4">
          <div className="flex items-center justify-center mb-8 mt-4">
            {isOpen ? (
              <h1 className="font-bold text-xl text-primary">Better Care</h1>
            ) : !isMobile && (
              <h1 className="font-bold text-xl text-primary">BC</h1>
            )}
          </div>

          <nav>
            {navItems.map((item) => (
              <NavItem
                key={item.to}
                to={item.to}
                label={item.label}
                icon={item.icon}
                isActive={location.pathname === item.to}
                onClick={isMobile ? () => setIsOpen(false) : undefined}
              />
            ))}
          </nav>
        </div>
      </div>
    </>
  );
}
