
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// TODO: Replace these with your actual Supabase project credentials
// Get them from: https://supabase.com/dashboard → Your Project → Settings → API
const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "REPLACE_WITH_YOUR_ACTUAL_ANON_KEY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
