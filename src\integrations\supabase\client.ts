
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4enBlaW56dHZ1cnRuY2FhcWRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTM5NTU3MzksImV4cCI6MjAyOTUzMTczOX0.QGNkCdHQUjnCYSy_vJhWZp8W5c7QJ5X9Y8Z1Z2Z3Z4Z";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
