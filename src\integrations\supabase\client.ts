
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9zaGxocmdhenRmYmlodXhlb3B1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0Mzc1ODIsImV4cCI6MjA2MjAxMzU4Mn0.ouZSe00DWjc0bSjO7FfLwOdoIlgDAyD7HErUjYjwB8o";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
