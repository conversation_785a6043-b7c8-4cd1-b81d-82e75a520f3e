import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formate une date au format "jour mois année" (ex: 02 Sept 2025)
 * @param dateString - La date à formater (string ou Date)
 * @returns La date formatée
 */
export function formatDate(dateString: string | Date): string {
  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

  // Définir les noms des mois en français abrégés
  const moisAbr = ['Janv', 'Févr', 'Mars', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sept', 'Oct', 'Nov', 'Déc'];

  // Formater le jour avec un zéro devant si nécessaire
  const jour = date.getDate().toString().padStart(2, '0');

  // Obtenir le mois abrégé
  const mois = moisAbr[date.getMonth()];

  // Obtenir l'année
  const annee = date.getFullYear();

  // Retourner la date formatée
  return `${jour} ${mois} ${annee}`;
}
