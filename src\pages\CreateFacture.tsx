
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import {
  createFacture,
  createFactureItems,
  getPatients,
  getSoins
} from "@/services/database";
import { FactureInsert, FactureItemInsert, Patient, Soin } from "@/types/app-types";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";



// Type pour les éléments de la facture temporaires (avant sauvegarde)
type TempF<PERSON>ureItem = {
  soinId: string;
  nom: string;
  quantite: number;
  prix: number;
  total: number;
};

export default function CreateFacture() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedPatient, setSelectedPatient] = useState<string | null>(null);
  const [factureItems, setFactureItems] = useState<TempFactureItem[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [itemQuantity, setItemQuantity] = useState<number>(1);
  const [factureDate, setFactureDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [factureStatut, setFactureStatut] = useState<string>("non payée");
  const [montantPaye, setMontantPaye] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [soins, setSoins] = useState<Soin[]>([]);

  // Calculer le total de la facture
  const total = factureItems.reduce((sum, item) => sum + item.total, 0);

  // Charger les données depuis Supabase
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [patientsData, soinsData] = await Promise.all([
          getPatients(),
          getSoins()
        ]);

        setPatients(patientsData);
        setSoins(soinsData);
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les données. Veuillez réessayer.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Ajouter un item à la facture
  const handleAddItem = () => {
    if (!selectedItemId) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un soin ou produit",
        variant: "destructive",
      });
      return;
    }

    if (itemQuantity <= 0) {
      toast({
        title: "Erreur",
        description: "La quantité doit être supérieure à 0",
        variant: "destructive",
      });
      return;
    }

    const selectedSoin = soins.find(s => s.id === selectedItemId);
    if (!selectedSoin) return;

    // Vérifier si le produit est déjà dans la facture
    const existingIndex = factureItems.findIndex(i => i.soinId === selectedSoin.id);

    if (existingIndex !== -1) {
      // Mettre à jour la quantité et le total
      const updatedItems = [...factureItems];
      updatedItems[existingIndex].quantite += itemQuantity;
      updatedItems[existingIndex].total = updatedItems[existingIndex].quantite * updatedItems[existingIndex].prix;
      setFactureItems(updatedItems);
    } else {
      // Ajouter un nouvel item
      setFactureItems([
        ...factureItems,
        {
          soinId: selectedSoin.id,
          nom: selectedSoin.nom,
          quantite: itemQuantity,
          prix: selectedSoin.prix,
          total: selectedSoin.prix * itemQuantity,
        },
      ]);
    }

    // Réinitialiser les champs
    setSelectedItemId(null);
    setItemQuantity(1);
  };

  // Supprimer un item de la facture
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...factureItems];
    updatedItems.splice(index, 1);
    setFactureItems(updatedItems);
  };

  // Gérer le changement de statut de la facture
  const handleStatutChange = (value: string) => {
    setFactureStatut(value);

    if (value === "payée") {
      setMontantPaye(total);
    } else if (value === "non payée") {
      setMontantPaye(0);
    }
  };

  // Enregistrer la facture
  const handleSaveFacture = async () => {
    if (!selectedPatient) {
      toast({
        title: "Erreur",
        description: "Veuillez sélectionner un patient",
        variant: "destructive",
      });
      return;
    }

    if (factureItems.length === 0) {
      toast({
        title: "Erreur",
        description: "La facture doit contenir au moins un soin ou produit",
        variant: "destructive",
      });
      return;
    }



    try {
      setLoading(true);

      // Générer un numéro de facture
      const factureNumero = `F${new Date().getFullYear()}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

      // Créer la facture dans Supabase
      const factureData: FactureInsert = {
        numero: factureNumero,
        patient_id: selectedPatient,
        date: factureDate,
        montant_total: total,
        statut: factureStatut as any,
        montant_paye: factureStatut === "payée" ? total : (factureStatut === "avance" ? montantPaye : null),
        notes: null
      };

      const facture = await createFacture(factureData);

      if (!facture) {
        throw new Error("Erreur lors de la création de la facture");
      }

      // Créer les items de la facture
      const factureItemsData: FactureItemInsert[] = factureItems.map(item => ({
        facture_id: facture.id,
        soin_id: item.soinId,
        nom: item.nom,
        quantite: item.quantite,
        prix_unitaire: item.prix,
        total: item.total
      }));

      await createFactureItems(factureItemsData);



      toast({
        title: "Facture enregistrée",
        description: "La facture a été enregistrée avec succès."
      });

      // Rediriger vers la liste des factures
      navigate("/factures");
    } catch (error) {
      console.error("Erreur lors de l'enregistrement de la facture:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement de la facture.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Nouvelle Facture</h1>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Chargement des données...</p>
          </div>
        </div>
      ) : (

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Informations Générales</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="patient">Patient</Label>
                    <Select
                      value={selectedPatient || undefined}
                      onValueChange={setSelectedPatient}
                    >
                      <SelectTrigger id="patient">
                        <SelectValue placeholder="Sélectionner un patient" />
                      </SelectTrigger>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id.toString()}>
                            {patient.nom} {patient.prenom}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="date">Date</Label>
                    <Input
                      id="date"
                      type="date"
                      value={factureDate}
                      onChange={(e) => setFactureDate(e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Éléments Facturés</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <Label htmlFor="soin" className="mb-2 block">Soin ou Produit</Label>
                    <Select
                      value={selectedItemId || undefined}
                      onValueChange={setSelectedItemId}
                    >
                      <SelectTrigger id="soin">
                        <SelectValue placeholder="Sélectionner un soin ou produit" />
                      </SelectTrigger>
                      <SelectContent>
                        {soins.map((soin) => (
                          <SelectItem key={soin.id} value={soin.id.toString()}>
                            {soin.nom} - {soin.prix.toFixed(0)} XAF
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="quantity" className="mb-2 block">Quantité</Label>
                    <div className="flex items-center">
                      <Input
                        id="quantity"
                        type="number"
                        min="1"
                        className="w-20"
                        value={itemQuantity}
                        onChange={(e) => setItemQuantity(parseInt(e.target.value) || 0)}
                      />
                      <Button
                        onClick={handleAddItem}
                        className="ml-2"
                        variant="outline"
                      >
                        <Plus size={16} className="mr-1" /> Ajouter
                      </Button>
                    </div>
                  </div>
                </div>

                {factureItems.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Désignation</TableHead>
                        <TableHead>Quantité</TableHead>
                        <TableHead>Prix unitaire</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {factureItems.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.nom}</TableCell>
                          <TableCell>{item.quantite}</TableCell>
                          <TableCell>{item.prix.toFixed(0)} XAF</TableCell>
                          <TableCell>{item.total.toFixed(0)} XAF</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveItem(index)}
                            >
                              <Trash2 size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucun élément ajouté à la facture
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div>
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle>Récapitulatif</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Statut de la facture</Label>
                  <Select
                    value={factureStatut}
                    onValueChange={handleStatutChange}
                  >
                    <SelectTrigger id="status">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="payée">Payée</SelectItem>
                      <SelectItem value="avance">Avance</SelectItem>
                      <SelectItem value="non payée">Non payée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {factureStatut === "avance" && (
                  <div>
                    <Label htmlFor="montant-paye">Montant payé</Label>
                    <Input
                      id="montant-paye"
                      type="number"
                      min="0"
                      step="0.01"
                      value={montantPaye}
                      onChange={(e) => setMontantPaye(parseFloat(e.target.value) || 0)}
                    />
                  </div>
                )}

                <div className="pt-4 border-t">
                  <div className="flex justify-between mb-2">
                    <span>Sous-total</span>
                    <span>{total.toFixed(0)} XAF</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>TVA (0%)</span>
                    <span>0.00 XAF</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg mt-4 pt-2 border-t">
                    <span>Total</span>
                    <span>{total.toFixed(0)} XAF</span>
                  </div>

                  {factureStatut === "avance" && (
                    <>
                      <div className="flex justify-between mt-4 pt-2 border-t">
                        <span>Payé</span>
                        <span>{montantPaye.toFixed(0)} XAF</span>
                      </div>
                      <div className="flex justify-between text-red-600 font-bold">
                        <span>Reste à payer</span>
                        <span>{(total - montantPaye).toFixed(0)} XAF</span>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  onClick={() => handleSaveFacture()}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Enregistrement...
                    </>
                  ) : (
                    "Enregistrer la facture"
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      )}


    </div>
  );
}
