
import HospitalManagement from "@/components/admin/HospitalManagement";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import {
  getFactures,
  getPatients
} from "@/services/database";
import { Facture, Patient } from "@/types/app-types";
import { DollarSign, FileText, Users } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

const StatCard = ({ title, value, icon, className }: {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  className?: string;
}) => (
  <Card className={className}>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
    </CardContent>
  </Card>
);

const RecentPatientRow = ({ patient }: { patient: Patient }) => (
  <Link to={`/patients/${patient.id}`}>
    <div className="flex items-center p-3 rounded-lg hover:bg-muted cursor-pointer">
      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold">
        {patient.nom.charAt(0)}{patient.prenom.charAt(0)}
      </div>
      <div className="ml-4">
        <p className="font-medium">{patient.nom} {patient.prenom}</p>
        <p className="text-sm text-muted-foreground">{patient.telephone}</p>
      </div>
    </div>
  </Link>
);

const RecentFactureRow = ({ facture, patientName }: { facture: Facture, patientName: string }) => {
  const getStatusClass = (statut: string) => {
    switch (statut) {
      case "payée": return "status-paid";
      case "avance": return "status-advance";
      case "non payée": return "status-unpaid";
      default: return "";
    }
  };

  return (
    <Link to={`/factures`}>
      <div className="flex items-center justify-between p-3 rounded-lg hover:bg-muted cursor-pointer">
        <div className="flex items-center">
          <div className="mr-4">
            <p className="font-medium">Facture #{facture.numero}</p>
            <p className="text-sm text-muted-foreground">{patientName}</p>
          </div>
        </div>
        <div className="flex items-center">
          <span className="mr-4 font-medium">{facture.montant_total} XAF</span>
          <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(facture.statut)}`}>
            {facture.statut}
          </span>
        </div>
      </div>
    </Link>
  );
};

export default function Dashboard() {
  const { toast } = useToast();
  const { isSuperAdmin } = useAuth();
  const [loading, setLoading] = useState(true);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [factures, setFactures] = useState<Facture[]>([]);
  const [patientMap, setPatientMap] = useState<Record<string, Patient>>({});
  const [stats, setStats] = useState({
    totalPatients: 0,
    totalFactures: 0,
    facturesNonPayees: 0
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load data from Supabase
        const patientsData = await getPatients();
        const facturesData = await getFactures();

        // Create patient map for easy lookup
        const patientMapping = patientsData.reduce((map, patient) => {
          map[patient.id] = patient;
          return map;
        }, {} as Record<string, Patient>);

        // Set state
        setPatients(patientsData);
        setFactures(facturesData);
        setPatientMap(patientMapping);

        // Calculate stats
        const facturesNonPayees = facturesData.filter(f => f.statut === "non payée").length;

        setStats({
          totalPatients: patientsData.length,
          totalFactures: facturesData.length,
          facturesNonPayees
        });

        // Show welcome toast
        toast({
          title: "Bienvenue sur Better Care Facturation",
          description: "Votre tableau de bord est prêt"
        });
      } catch (error) {
        console.error("Error loading data:", error);
        toast({
          title: "Erreur de chargement",
          description: "Impossible de charger les données",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Chargement des données...</p>
      </div>
    );
  }

  // Si c'est un super admin, afficher la gestion des hôpitaux
  if (isSuperAdmin) {
    return (
      <div>
        <h1 className="text-3xl font-bold mb-6">Tableau de bord - Super Administrateur</h1>
        <HospitalManagement />
      </div>
    );
  }

  // Pour les admins d'hôpital, afficher le tableau de bord normal
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Tableau de bord</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <StatCard
          title="Total Patients"
          value={stats.totalPatients}
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
        />
        <StatCard
          title="Total Factures"
          value={stats.totalFactures}
          icon={<FileText className="h-4 w-4 text-muted-foreground" />}
        />
        <StatCard
          title="Factures Non Payées"
          value={stats.facturesNonPayees}
          icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
          className="border-destructive/20"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Patients Récents</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            {patients.slice(0, 5).map(patient => (
              <RecentPatientRow key={patient.id} patient={patient} />
            ))}
            <Link to="/patients">
              <div className="text-center mt-4 text-primary hover:underline">
                Voir tous les patients
              </div>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Dernières Factures</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            {factures.slice(0, 5).map(facture => {
              const patient = patientMap[facture.patient_id];
              const patientName = patient ? `${patient.prenom} ${patient.nom}` : "Patient inconnu";

              return (
                <RecentFactureRow
                  key={facture.id}
                  facture={facture}
                  patientName={patientName}
                />
              );
            })}
            <Link to="/factures">
              <div className="text-center mt-4 text-primary hover:underline">
                Voir toutes les factures
              </div>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
