import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getHospitalById, updateHospital, uploadHospitalLogo } from '@/services/hospitalService';
import { Hospital } from '@/types/app-types';
import { ArrowLeft, Building2, Save, Upload, Image } from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const hospitalSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit contenir au moins 2 caractères" }),
  contact_email: z.string().email({ message: "Email invalide" }).optional().or(z.literal("")),
  contact_phone: z.string().optional(),
  address: z.string().optional(),
  subscription_status: z.enum(['active', 'inactive', 'suspended']),
  account_status: z.enum(['active', 'inactive']),
});

type HospitalFormData = z.infer<typeof hospitalSchema>;

export default function EditHospital() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [hospital, setHospital] = useState<Hospital | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const form = useForm<HospitalFormData>({
    resolver: zodResolver(hospitalSchema),
    defaultValues: {
      name: '',
      contact_email: '',
      contact_phone: '',
      address: '',
      subscription_status: 'active',
      account_status: 'active',
    },
  });

  useEffect(() => {
    if (id) {
      loadHospital();
    }
  }, [id]);

  const loadHospital = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const hospitalData = await getHospitalById(id);
      
      if (!hospitalData) {
        toast({
          title: "Erreur",
          description: "Hôpital introuvable.",
          variant: "destructive"
        });
        navigate('/super-admin/hospitals');
        return;
      }

      setHospital(hospitalData);
      setLogoPreview(hospitalData.logo_url);
      
      // Remplir le formulaire avec les données existantes
      form.reset({
        name: hospitalData.name,
        contact_email: hospitalData.contact_email || '',
        contact_phone: hospitalData.contact_phone || '',
        address: hospitalData.address || '',
        subscription_status: hospitalData.subscription_status as 'active' | 'inactive' | 'suspended',
        account_status: hospitalData.account_status as 'active' | 'inactive',
      });
    } catch (error) {
      console.error('Error loading hospital:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'hôpital.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: HospitalFormData) => {
    if (!id || !hospital) return;

    try {
      setSaving(true);

      // Upload du logo si un nouveau fichier a été sélectionné
      let logoUrl = hospital.logo_url;
      if (logoFile) {
        logoUrl = await uploadHospitalLogo(id, logoFile);
      }

      // Mise à jour des données de l'hôpital
      const updateData = {
        name: data.name,
        contact_email: data.contact_email || null,
        contact_phone: data.contact_phone || null,
        address: data.address || null,
        subscription_status: data.subscription_status,
        account_status: data.account_status,
        logo_url: logoUrl,
      };

      const updatedHospital = await updateHospital(id, updateData);
      
      if (!updatedHospital) {
        throw new Error('Erreur lors de la mise à jour');
      }

      setHospital(updatedHospital);
      
      toast({
        title: "Hôpital mis à jour",
        description: "Les informations de l'hôpital ont été mises à jour avec succès.",
      });

      navigate(`/super-admin/hospitals/${id}`);
    } catch (error: any) {
      console.error('Error updating hospital:', error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour l'hôpital.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!hospital) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Hôpital introuvable</h1>
          <p className="text-gray-600 mt-2">L'hôpital demandé n'existe pas.</p>
          <Button onClick={() => navigate('/super-admin/hospitals')} className="mt-4">
            Retour à la liste
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate(`/super-admin/hospitals/${id}`)}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Modifier l'Hôpital</h1>
          <p className="text-gray-600">Modifiez les informations de {hospital.name}</p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Logo de l'hôpital */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                Logo de l'Hôpital
              </CardTitle>
              <CardDescription>
                Téléchargez le logo de l'hôpital (format recommandé: PNG, JPG)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {logoPreview ? (
                    <img 
                      src={logoPreview} 
                      alt="Logo preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Building2 className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="mb-2"
                  />
                  <p className="text-sm text-gray-600">
                    Formats acceptés: PNG, JPG, JPEG. Taille maximale: 5MB
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informations de l'hôpital */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Informations de l'Hôpital
              </CardTitle>
              <CardDescription>
                Modifiez les informations principales de l'hôpital
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom de l'hôpital *</FormLabel>
                    <FormControl>
                      <Input placeholder="Centre Hospitalier de..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email de contact</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Téléphone</FormLabel>
                      <FormControl>
                        <Input placeholder="+237 6XX XXX XXX" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Adresse</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Adresse complète de l'hôpital..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="account_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Statut du compte</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner le statut" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Actif</SelectItem>
                          <SelectItem value="inactive">Inactif</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subscription_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Statut d'abonnement</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner l'abonnement" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Actif</SelectItem>
                          <SelectItem value="inactive">Inactif</SelectItem>
                          <SelectItem value="suspended">Suspendu</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/super-admin/hospitals/${id}`)}
              disabled={saving}
            >
              Annuler
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enregistrement...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Enregistrer
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
