
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { formatDate } from "@/lib/utils";
import { getFactureItemsByFactureId, getFactures, getPatientById } from "@/services/database";
import { Facture } from "@/types/app-types";
import { Edit, FilePlus, Loader2, MessageSquare, Printer, Search } from "lucide-react";
import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";

export default function Factures() {
  const [factures, setFactures] = useState<Facture[]>([]);
  const [facturesWithPatients, setFacturesWithPatients] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Charger les factures depuis Supabase
  useEffect(() => {
    const fetchFactures = async () => {
      try {
        setLoading(true);
        const facturesData = await getFactures();
        setFactures(facturesData);

        // Récupérer les informations des patients pour chaque facture
        const facturesWithPatientInfo = await Promise.all(
          facturesData.map(async (facture) => {
            const patient = await getPatientById(facture.patient_id);
            return {
              ...facture,
              patientNom: patient ? `${patient.nom} ${patient.prenom}` : 'Patient inconnu',
              patientId: facture.patient_id,
              patient: patient
            };
          })
        );

        setFacturesWithPatients(facturesWithPatientInfo);
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors du chargement des factures:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les factures. Veuillez réessayer.",
          variant: "destructive"
        });
        setLoading(false);
      }
    };

    fetchFactures();
  }, [toast]);

  // Filtrer les factures selon le terme de recherche
  const filteredFactures = facturesWithPatients.filter(facture =>
    facture.patientNom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    facture.numero.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filtrer par statut
  const facturesPayees = filteredFactures.filter(f => f.statut === "payée");
  const facturesAvance = filteredFactures.filter(f => f.statut === "avance");
  const facturesNonPayees = filteredFactures.filter(f => f.statut === "non payée");

  const handlePrintFacture = async (facture: any) => {
    try {
      setLoading(true);

      // Récupérer les items de la facture
      const factureItems = await getFactureItemsByFactureId(facture.id);

      // Créer une nouvelle fenêtre pour l'impression
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast({
          title: "Erreur",
          description: "Impossible d'ouvrir la fenêtre d'impression. Vérifiez les paramètres de votre navigateur.",
          variant: "destructive"
        });
        return;
      }

      // Récupérer le patient associé à la facture
      const patient = facture.patient;

      // Formater la date pour l'impression
      const formattedDate = formatDate(facture.date);

      // Contenu HTML de la facture à imprimer
      const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Facture #${facture.id}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .facture-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }
          .facture-details, .patient-details {
            width: 48%;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
          }
          table, th, td {
            border: 1px solid #ddd;
          }
          th, td {
            padding: 10px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
          }
          .total-section {
            margin-top: 30px;
            text-align: right;
          }
          .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
          }
          .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
          }
          .status-paid { background-color: #d4edda; color: #155724; }
          .status-advance { background-color: #fff3cd; color: #856404; }
          .status-unpaid { background-color: #f8d7da; color: #721c24; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Better Care</h1>
          <p>Centre de santé et de bien-être</p>
        </div>

        <div class="facture-info">
          <div class="facture-details">
            <h2>Facture #${facture.numero}</h2>
            <p>Date: ${formattedDate}</p>
            <p>Statut:
              <span class="status ${facture.statut === 'payée' ? 'status-paid' : facture.statut === 'avance' ? 'status-advance' : 'status-unpaid'}">
                ${facture.statut}
              </span>
            </p>
          </div>
          <div class="patient-details">
            <h3>Patient</h3>
            <p>Nom: ${patient ? patient.nom + ' ' + patient.prenom : 'N/A'}</p>
            <p>Téléphone: ${patient ? patient.telephone : 'N/A'}</p>
            <p>Email: ${patient ? patient.email || 'N/A' : 'N/A'}</p>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>Désignation</th>
              <th>Quantité</th>
              <th>Prix unitaire (XAF)</th>
              <th>Total (XAF)</th>
            </tr>
          </thead>
          <tbody>
            ${factureItems.map(item => `
              <tr>
                <td>${item.nom}</td>
                <td>${item.quantite}</td>
                <td>${item.prix_unitaire.toFixed(0)}</td>
                <td>${item.total.toFixed(0)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="total-section">
          <p><strong>Total: ${facture.montant_total.toFixed(0)} XAF</strong></p>
          ${facture.statut === 'avance' ? `
            <p>Montant payé: ${facture.montant_paye ? facture.montant_paye.toFixed(0) : '0'} XAF</p>
            <p><strong>Reste à payer: ${(facture.montant_total - (facture.montant_paye || 0)).toFixed(0)} XAF</strong></p>
          ` : ''}
        </div>

        <div class="footer">
          <p>Better Care - Centre de santé</p>
          <p>Merci de votre confiance!</p>
        </div>
      </body>
      </html>
    `;

      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Attendre que la page soit chargée avant d'imprimer
      printWindow.onload = function () {
        printWindow.print();

        toast({
          title: "Impression lancée",
          description: `La facture ${facture.numero} est en cours d'impression.`
        });
        setLoading(false);
      };
    } catch (error) {
      console.error("Erreur lors de l'impression:", error);
      toast({
        title: "Erreur",
        description: "Impossible d'imprimer la facture. Veuillez réessayer.",
        variant: "destructive"
      });
      setLoading(false);
    }
  };

  const handleEditFacture = (facture: any) => {
    if (facture.statut === "payée") {
      toast({
        title: "Modification impossible",
        description: "Les factures payées ne peuvent pas être modifiées.",
        variant: "destructive"
      });
      return;
    }

    // Rediriger vers la page de modification de facture avec l'ID de la facture
    navigate(`/edit-facture/${facture.id}`);
  };

  const handleSendSMS = (facture: any) => {
    // Dans une vraie application, cela ouvrirait un formulaire pour envoyer un SMS
    const patient = facture.patient;

    toast({
      title: "SMS de relance envoyé",
      description: `Un SMS a été envoyé à ${patient?.prenom} ${patient?.nom} concernant la facture ${facture.numero}.`
    });
  };

  // Rendu d'une carte facture
  const FactureCard: React.FC<{ facture: any }> = ({ facture }) => {
    const getStatusClass = (statut: string) => {
      switch (statut) {
        case "payée": return "status-paid";
        case "avance": return "status-advance";
        case "non payée": return "status-unpaid";
        default: return "";
      }
    };

    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between">
            <CardTitle className="text-lg">Facture #{facture.numero}</CardTitle>
            <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(facture.statut)}`}>
              {facture.statut}
            </span>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between mb-4">
            <div>
              <div className="text-sm text-muted-foreground">Patient</div>
              <div className="font-medium">{facture.patientNom}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Date</div>
              <div className="font-medium">{formatDate(facture.date)}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Montant</div>
              <div className="font-medium">{facture.montant_total.toFixed(0)} XAF</div>
            </div>
            {facture.statut === "avance" && (
              <div>
                <div className="text-sm text-muted-foreground">Payé</div>
                <div className="font-medium">{facture.montant_paye ? facture.montant_paye.toFixed(0) : '0'} XAF</div>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handlePrintFacture(facture)}
            >
              <Printer size={16} className="mr-2" /> Imprimer
            </Button>

            {(facture.statut === "non payée" || facture.statut === "avance") && (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEditFacture(facture)}
                >
                  <Edit size={16} className="mr-2" /> Modifier
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleSendSMS(facture)}
                >
                  <MessageSquare size={16} className="mr-2" /> Relance SMS
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Rendu des listes de factures
  const renderFacturesList = (factures: any[]) => {
    if (factures.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          Aucune facture trouvée
        </div>
      );
    }

    return (
      <div>
        {factures.map(facture => (
          <FactureCard key={facture.id} facture={facture} />
        ))}
      </div>
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Factures</h1>
        <Link to="/create-facture">
          <Button>
            <FilePlus size={16} className="mr-2" />
            Nouvelle Facture
          </Button>
        </Link>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Chargement des factures...</p>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-6 max-w-md">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher une facture..."
                className="pl-8"
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <Tabs defaultValue="toutes">
            <TabsList className="mb-6">
              <TabsTrigger value="toutes">Toutes ({filteredFactures.length})</TabsTrigger>
              <TabsTrigger value="payees">Payées ({facturesPayees.length})</TabsTrigger>
              <TabsTrigger value="avance">Avance ({facturesAvance.length})</TabsTrigger>
              <TabsTrigger value="non-payees">Non payées ({facturesNonPayees.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="toutes">
              {renderFacturesList(filteredFactures)}
            </TabsContent>

            <TabsContent value="payees">
              {renderFacturesList(facturesPayees)}
            </TabsContent>

            <TabsContent value="avance">
              {renderFacturesList(facturesAvance)}
            </TabsContent>

            <TabsContent value="non-payees">
              {renderFacturesList(facturesNonPayees)}
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
