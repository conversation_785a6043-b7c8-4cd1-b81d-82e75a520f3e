import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { getHospitalById } from '@/services/hospitalService';
import { getUsersByHospital } from '@/services/userService';
import { Hospital, UserWithHospital } from '@/types/app-types';
import { 
  ArrowLeft, 
  Building2, 
  Edit, 
  Mail, 
  Phone, 
  MapPin, 
  Users, 
  DollarSign, 
  FileText,
  Calendar,
  User,
  Shield
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

export default function HospitalDetails() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [hospital, setHospital] = useState<Hospital | null>(null);
  const [users, setUsers] = useState<UserWithHospital[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadHospitalData();
    }
  }, [id]);

  const loadHospitalData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const [hospitalData, usersData] = await Promise.all([
        getHospitalById(id),
        getUsersByHospital(id)
      ]);

      if (!hospitalData) {
        toast({
          title: "Erreur",
          description: "Hôpital introuvable.",
          variant: "destructive"
        });
        navigate('/super-admin/hospitals');
        return;
      }

      setHospital(hospitalData);
      setUsers(usersData);
    } catch (error) {
      console.error('Error loading hospital data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'hôpital.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspendu</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Badge variant="destructive">Super Admin</Badge>;
      case 'hospital_admin':
        return <Badge variant="default">Admin Hôpital</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!hospital) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Hôpital introuvable</h1>
          <p className="text-gray-600 mt-2">L'hôpital demandé n'existe pas.</p>
          <Button onClick={() => navigate('/super-admin/hospitals')} className="mt-4">
            Retour à la liste
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => navigate('/super-admin/hospitals')}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
              {hospital.logo_url ? (
                <img 
                  src={hospital.logo_url} 
                  alt={hospital.name}
                  className="w-14 h-14 object-cover rounded"
                />
              ) : (
                <Building2 className="h-8 w-8 text-gray-400" />
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold">{hospital.name}</h1>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusBadge(hospital.account_status)}
                {getStatusBadge(hospital.subscription_status)}
              </div>
            </div>
          </div>
        </div>
        <Link to={`/super-admin/hospitals/${hospital.id}/edit`}>
          <Button>
            <Edit className="mr-2 h-4 w-4" />
            Modifier
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informations générales */}
        <Card>
          <CardHeader>
            <CardTitle>Informations Générales</CardTitle>
            <CardDescription>
              Détails de l'hôpital et informations de contact
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {hospital.contact_email && (
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{hospital.contact_email}</span>
                </div>
              )}
              
              {hospital.contact_phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{hospital.contact_phone}</span>
                </div>
              )}
              
              {hospital.address && (
                <div className="flex items-start space-x-3">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                  <span className="text-sm">{hospital.address}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm">
                  Créé le {formatDate(hospital.created_at)}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium mb-3">Statuts</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Compte:</span>
                  {getStatusBadge(hospital.account_status)}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Abonnement:</span>
                  {getStatusBadge(hospital.subscription_status)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Utilisateurs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Utilisateurs ({users.length})
            </CardTitle>
            <CardDescription>
              Liste des utilisateurs ayant accès à cet hôpital
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">{user.full_name || user.email}</p>
                      <p className="text-xs text-gray-600">{user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getRoleBadge(user.role)}
                  </div>
                </div>
              ))}
              
              {users.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">Aucun utilisateur assigné</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistiques détaillées */}
      <Card>
        <CardHeader>
          <CardTitle>Statistiques</CardTitle>
          <CardDescription>
            Aperçu de l'activité de l'hôpital
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <p className="text-2xl font-bold">0</p>
              <p className="text-sm text-gray-600">Patients</p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <FileText className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <p className="text-2xl font-bold">0</p>
              <p className="text-sm text-gray-600">Factures</p>
            </div>
            
            <div className="text-center p-4 border rounded-lg">
              <DollarSign className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
              <p className="text-2xl font-bold">0 XAF</p>
              <p className="text-sm text-gray-600">Chiffre d'affaires</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
