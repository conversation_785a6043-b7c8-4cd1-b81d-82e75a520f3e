import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { getHospitalsWithStats, toggleHospitalStatus, updateSubscriptionStatus } from '@/services/hospitalService';
import { HospitalWithStats } from '@/types/app-types';
import { 
  Building2, 
  Plus, 
  Search, 
  Eye, 
  Edit, 
  Users, 
  DollarSign, 
  FileText,
  MoreVertical,
  Power,
  PowerOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function HospitalList() {
  const [hospitals, setHospitals] = useState<HospitalWithStats[]>([]);
  const [filteredHospitals, setFilteredHospitals] = useState<HospitalWithStats[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadHospitals();
  }, []);

  useEffect(() => {
    const filtered = hospitals.filter(hospital =>
      hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hospital.contact_email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredHospitals(filtered);
  }, [hospitals, searchTerm]);

  const loadHospitals = async () => {
    try {
      setLoading(true);
      const data = await getHospitalsWithStats();
      setHospitals(data);
    } catch (error) {
      console.error('Error loading hospitals:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger la liste des hôpitaux.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (hospitalId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await toggleHospitalStatus(hospitalId, newStatus);
      
      toast({
        title: "Statut mis à jour",
        description: `L'hôpital a été ${newStatus === 'active' ? 'activé' : 'désactivé'}.`
      });
      
      await loadHospitals();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de modifier le statut de l'hôpital.",
        variant: "destructive"
      });
    }
  };

  const handleUpdateSubscription = async (hospitalId: string, status: 'active' | 'inactive' | 'suspended') => {
    try {
      await updateSubscriptionStatus(hospitalId, status);
      
      toast({
        title: "Abonnement mis à jour",
        description: `L'abonnement a été mis à jour vers ${status}.`
      });
      
      await loadHospitals();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Impossible de modifier l'abonnement.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspendu</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSubscriptionBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspendu</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Gestion des Hôpitaux</h1>
          <p className="text-gray-600">Gérez tous les hôpitaux de la plateforme</p>
        </div>
        <Link to="/super-admin/hospitals/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Nouvel Hôpital
          </Button>
        </Link>
      </div>

      {/* Barre de recherche */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un hôpital..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hôpitaux</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{hospitals.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hôpitaux Actifs</CardTitle>
            <Power className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hospitals.filter(h => h.account_status === 'active').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hospitals.reduce((sum, h) => sum + (h.patient_count || 0), 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hospitals.reduce((sum, h) => sum + (h.total_revenue || 0), 0).toLocaleString()} XAF
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Liste des hôpitaux */}
      <Card>
        <CardHeader>
          <CardTitle>Hôpitaux ({filteredHospitals.length})</CardTitle>
          <CardDescription>
            Liste de tous les hôpitaux enregistrés sur la plateforme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredHospitals.map((hospital) => (
              <div key={hospital.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    {hospital.logo_url ? (
                      <img 
                        src={hospital.logo_url} 
                        alt={hospital.name}
                        className="w-10 h-10 object-cover rounded"
                      />
                    ) : (
                      <Building2 className="h-6 w-6 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg">{hospital.name}</h3>
                    <p className="text-sm text-gray-600">{hospital.contact_email}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{hospital.patient_count} patients</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FileText className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{hospital.facture_count} factures</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{(hospital.total_revenue || 0).toLocaleString()} XAF</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="flex flex-col space-y-1">
                    {getStatusBadge(hospital.account_status)}
                    {getSubscriptionBadge(hospital.subscription_status)}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Link to={`/super-admin/hospitals/${hospital.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Voir
                      </Button>
                    </Link>
                    
                    <Link to={`/super-admin/hospitals/${hospital.id}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Modifier
                      </Button>
                    </Link>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleToggleStatus(hospital.id, hospital.account_status)}
                        >
                          {hospital.account_status === 'active' ? (
                            <>
                              <PowerOff className="mr-2 h-4 w-4" />
                              Désactiver
                            </>
                          ) : (
                            <>
                              <Power className="mr-2 h-4 w-4" />
                              Activer
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleUpdateSubscription(hospital.id, 'suspended')}
                          className="text-red-600"
                        >
                          Suspendre abonnement
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
            
            {filteredHospitals.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {searchTerm ? 'Aucun hôpital trouvé pour cette recherche.' : 'Aucun hôpital enregistré.'}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
