import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { getHospitalById, updateHospital, uploadHospitalLogo } from '@/services/hospitalService';
import { Hospital } from '@/types/app-types';
import { Building2, Save, Upload, Image, Calendar, Mail, Phone, MapPin } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const hospitalProfileSchema = z.object({
  name: z.string().min(2, { message: "Le nom doit contenir au moins 2 caractères" }),
  contact_email: z.string().email({ message: "Email invalide" }).optional().or(z.literal("")),
  contact_phone: z.string().optional(),
  address: z.string().optional(),
});

type HospitalProfileFormData = z.infer<typeof hospitalProfileSchema>;

export default function HospitalProfile() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [hospital, setHospital] = useState<Hospital | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const form = useForm<HospitalProfileFormData>({
    resolver: zodResolver(hospitalProfileSchema),
    defaultValues: {
      name: '',
      contact_email: '',
      contact_phone: '',
      address: '',
    },
  });

  useEffect(() => {
    if (user?.hospital_id) {
      loadHospital();
    }
  }, [user]);

  const loadHospital = async () => {
    if (!user?.hospital_id) return;

    try {
      setLoading(true);
      const hospitalData = await getHospitalById(user.hospital_id);
      
      if (!hospitalData) {
        toast({
          title: "Erreur",
          description: "Impossible de charger les informations de l'hôpital.",
          variant: "destructive"
        });
        return;
      }

      setHospital(hospitalData);
      setLogoPreview(hospitalData.logo_url);
      
      // Remplir le formulaire avec les données existantes
      form.reset({
        name: hospitalData.name,
        contact_email: hospitalData.contact_email || '',
        contact_phone: hospitalData.contact_phone || '',
        address: hospitalData.address || '',
      });
    } catch (error) {
      console.error('Error loading hospital:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données de l'hôpital.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Vérifier la taille du fichier (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Fichier trop volumineux",
          description: "La taille du fichier ne doit pas dépasser 5MB.",
          variant: "destructive"
        });
        return;
      }

      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: HospitalProfileFormData) => {
    if (!user?.hospital_id || !hospital) return;

    try {
      setSaving(true);

      // Upload du logo si un nouveau fichier a été sélectionné
      let logoUrl = hospital.logo_url;
      if (logoFile) {
        logoUrl = await uploadHospitalLogo(user.hospital_id, logoFile);
      }

      // Mise à jour des données de l'hôpital
      const updateData = {
        name: data.name,
        contact_email: data.contact_email || null,
        contact_phone: data.contact_phone || null,
        address: data.address || null,
        logo_url: logoUrl,
      };

      const updatedHospital = await updateHospital(user.hospital_id, updateData);
      
      if (!updatedHospital) {
        throw new Error('Erreur lors de la mise à jour');
      }

      setHospital(updatedHospital);
      setLogoFile(null);
      
      toast({
        title: "Profil mis à jour",
        description: "Les informations de votre hôpital ont été mises à jour avec succès.",
      });
    } catch (error: any) {
      console.error('Error updating hospital profile:', error);
      toast({
        title: "Erreur",
        description: error.message || "Impossible de mettre à jour le profil.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspendu</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!hospital) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Profil non disponible</h1>
          <p className="text-gray-600 mt-2">Impossible de charger les informations de votre hôpital.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Profil de l'Hôpital</h1>
        <p className="text-gray-600">Gérez les informations de votre hôpital</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations du compte */}
        <Card>
          <CardHeader>
            <CardTitle>Statut du Compte</CardTitle>
            <CardDescription>
              Informations sur votre abonnement et statut
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Statut du compte:</span>
                {getStatusBadge(hospital.account_status)}
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Abonnement:</span>
                {getStatusBadge(hospital.subscription_status)}
              </div>
              <div className="flex items-center space-x-3 pt-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  Créé le {formatDate(hospital.created_at)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Formulaire de modification */}
        <div className="lg:col-span-2">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Logo de l'hôpital */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Image className="h-5 w-5" />
                    Logo de l'Hôpital
                  </CardTitle>
                  <CardDescription>
                    Téléchargez le logo de votre hôpital (PNG, JPG - 5MB max)
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                      {logoPreview ? (
                        <img 
                          src={logoPreview} 
                          alt="Logo preview"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Building2 className="h-8 w-8 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoChange}
                        className="mb-2"
                      />
                      <p className="text-sm text-gray-600">
                        Formats acceptés: PNG, JPG, JPEG. Taille maximale: 5MB
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Informations de l'hôpital */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Informations de l'Hôpital
                  </CardTitle>
                  <CardDescription>
                    Modifiez les informations de votre hôpital
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom de l'hôpital *</FormLabel>
                        <FormControl>
                          <Input placeholder="Centre Hospitalier de..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="contact_email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email de contact</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contact_phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Téléphone</FormLabel>
                          <FormControl>
                            <Input placeholder="+237 6XX XXX XXX" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Adresse</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Adresse complète de l'hôpital..."
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex justify-end">
                <Button type="submit" disabled={saving}>
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Enregistrement...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Enregistrer les modifications
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
