import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { formatDate } from "@/lib/utils";
import { getFacturesByPatient } from "@/services/facturesService";
import { getPatientById } from "@/services/patientsService";
import { Facture, Patient } from "@/types/app-types";
import { ArrowLeft, Edit, Mail, MapPin, Phone, Printer } from "lucide-react";
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

export default function PatientDetails() {
  const { id } = useParams<{ id: string }>();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [patientFactures, setPatientFactures] = useState<Facture[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    async function loadData() {
      if (!id) return;

      try {
        // Fetch patient data
        const patientData = await getPatientById(id);
        setPatient(patientData);

        // Fetch patient's invoices
        const factures = await getFacturesByPatient(id);
        setPatientFactures(factures);
      } catch (error) {
        console.error("Error loading patient data:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les données du patient",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [id, toast]);

  const getStatusClass = (statut: string) => {
    switch (statut) {
      case "payée": return "status-paid";
      case "avance": return "status-advance";
      case "non payée": return "status-unpaid";
      default: return "";
    }
  };

  // Fonction pour gérer l'impression de la facture
  const handlePrintFacture = (factureId: string) => {
    // Rediriger vers la page d'impression de la facture
    window.open(`/print-facture/${factureId}`, '_blank');
  };

  // Filtrer les factures selon leur statut
  const facturesPayees = patientFactures.filter(f => f.statut === "payée");
  const facturesAvance = patientFactures.filter(f => f.statut === "avance");
  const facturesNonPayees = patientFactures.filter(f => f.statut === "non payée");

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Chargement...</p>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl mb-4">Patient non trouvé</h2>
        <Link to="/patients">
          <Button>Retour à la liste des patients</Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link to="/patients">
          <Button variant="ghost">
            <ArrowLeft size={18} className="mr-2" />
            Retour
          </Button>
        </Link>
        <h1 className="text-3xl font-bold ml-4">
          {patient.nom} {patient.prenom}
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex justify-between">
              <span>Informations Patient</span>
              <Button variant="ghost" size="icon">
                <Edit size={16} />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start">
              <Phone className="h-5 w-5 mr-2 text-muted-foreground" />
              <div>
                <div className="font-medium">Téléphone</div>
                <div>{patient.telephone}</div>
              </div>
            </div>

            <div className="flex items-start">
              <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
              <div>
                <div className="font-medium">Email</div>
                <div>{patient.email || "Non renseigné"}</div>
              </div>
            </div>

            <div className="flex items-start">
              <MapPin className="h-5 w-5 mr-2 text-muted-foreground" />
              <div>
                <div className="font-medium">Adresse</div>
                <div>{patient.adresse || "Non renseignée"}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg">Récapitulatif Financier</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="border rounded-md p-4">
                <div className="text-sm text-muted-foreground">Nombre de factures</div>
                <div className="text-2xl font-bold">{patientFactures.length}</div>
              </div>
              <div className="border rounded-md p-4">
                <div className="text-sm text-muted-foreground">Total facturé</div>
                <div className="text-2xl font-bold">
                  {patientFactures.reduce((sum, f) => sum + f.montant_total, 0).toFixed(2)} XAF
                </div>
              </div>
              <div className="border rounded-md p-4">
                <div className="text-sm text-muted-foreground">Paiements en attente</div>
                <div className="text-2xl font-bold text-red-600">
                  {patientFactures
                    .filter(f => f.statut !== "payée")
                    .reduce((sum, f) => {
                      if (f.statut === "avance") {
                        return sum + (f.montant_total - (f.montant_paye || 0));
                      }
                      return sum + f.montant_total;
                    }, 0)
                    .toFixed(2)} XAF
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>Historique des Factures</span>
            <Link to="/create-facture">
              <Button size="sm">
                Nouvelle Facture
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="toutes">
            <TabsList className="mb-4">
              <TabsTrigger value="toutes">Toutes ({patientFactures.length})</TabsTrigger>
              <TabsTrigger value="payees">Payées ({facturesPayees.length})</TabsTrigger>
              <TabsTrigger value="avance">Avance ({facturesAvance.length})</TabsTrigger>
              <TabsTrigger value="non-payees">Non payées ({facturesNonPayees.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="toutes">
              {renderFacturesList(patientFactures)}
            </TabsContent>

            <TabsContent value="payees">
              {renderFacturesList(facturesPayees)}
            </TabsContent>

            <TabsContent value="avance">
              {renderFacturesList(facturesAvance)}
            </TabsContent>

            <TabsContent value="non-payees">
              {renderFacturesList(facturesNonPayees)}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );

  function renderFacturesList(factures: Facture[]) {
    if (factures.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          Aucune facture trouvée
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {factures.map(facture => (
          <div key={facture.id} className="flex items-center justify-between border p-4 rounded-lg">
            <div>
              <div className="font-medium">Facture #{facture.numero}</div>
              <div className="text-sm text-muted-foreground">Date: {formatDate(facture.date)}</div>
            </div>

            <div className="flex items-center">
              <div className="mr-4 text-right">
                <div className="font-medium">{facture.montant_total.toFixed(2)} XAF</div>
                {facture.statut === "avance" && facture.montant_paye && (
                  <div className="text-sm text-muted-foreground">
                    Payé: {facture.montant_paye.toFixed(2)} XAF
                  </div>
                )}
              </div>

              <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(facture.statut)}`}>
                {facture.statut}
              </span>

              <Button 
                variant="ghost" 
                size="icon" 
                className="ml-2" 
                onClick={() => handlePrintFacture(facture.id)}
                title="Imprimer la facture"
              >
                <Printer size={16} />
              </Button>
            </div>
          </div>
        ))}
      </div>
    );
  }
}
