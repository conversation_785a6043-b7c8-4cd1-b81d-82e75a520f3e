
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { createPatientForHospital, getPatientsByHospital } from "@/services/multiTenantDatabase";
import { Patient } from "@/types/app-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Mail, MapPin, Phone, Plus, Search } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import * as z from "zod";

// Schéma de validation pour le patient
const patientSchema = z.object({
  nom: z.string().min(2, { message: "Le nom doit contenir au moins 2 caractères" }),
  prenom: z.string().min(2, { message: "Le prénom doit contenir au moins 2 caractères" }),
  telephone: z.string().min(8, { message: "Le numéro de téléphone doit contenir au moins 8 caractères" }),
  email: z.string().email({ message: "L'adresse email n'est pas valide" }).optional().or(z.literal("")),
  adresse: z.string().optional().or(z.literal("")),
});

export default function Patients() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        const data = await getPatientsByHospital();
        setPatients(data);
      } catch (error) {
        console.error("Error loading patients:", error);
        toast({
          title: "Erreur de chargement",
          description: "Impossible de charger la liste des patients",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [toast]);

  // Initialiser le formulaire
  const form = useForm<z.infer<typeof patientSchema>>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      nom: "",
      prenom: "",
      telephone: "",
      email: "",
      adresse: "",
    },
  });

  // Filtrer les patients selon le terme de recherche
  const filteredPatients = patients.filter(patient =>
    patient.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.telephone.includes(searchTerm)
  );

  // Gérer la soumission du formulaire
  const onSubmit = async (values: z.infer<typeof patientSchema>) => {
    try {
      if (editingId) {
        // Mode édition: mettre à jour un patient existant
        const updatedPatient = await updatePatientForHospital(editingId, {
          nom: values.nom,
          prenom: values.prenom,
          telephone: values.telephone,
          email: values.email || null,
          adresse: values.adresse || null,
        });

        if (updatedPatient) {
          // Mettre à jour la liste locale
          setPatients(patients.map(p => p.id === editingId ? updatedPatient : p));

          toast({
            title: "Patient mis à jour",
            description: `${values.prenom} ${values.nom} a été modifié avec succès.`
          });
        }
      } else {
        // Nouveau patient: ajouter un nouveau patient
        const newPatient = await createPatientForHospital({
          nom: values.nom,
          prenom: values.prenom,
          telephone: values.telephone,
          email: values.email || null,
          adresse: values.adresse || null,
        });

        // Mettre à jour la liste locale
        setPatients([...patients, newPatient]);

        toast({
          title: "Patient ajouté",
          description: `${values.prenom} ${values.nom} a été ajouté avec succès.`
        });
      }

      // Réinitialiser le formulaire et fermer la boîte de dialogue
      form.reset();
      setEditingId(null);
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving patient:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement du patient.",
        variant: "destructive"
      });
    }
  };

  const handleViewPatient = (patientId: string) => {
    navigate(`/patients/${patientId}`);
  };

  const handleEditPatient = (patient: Patient) => {
    setEditingId(patient.id);
    form.reset({
      nom: patient.nom,
      prenom: patient.prenom,
      telephone: patient.telephone,
      email: patient.email || "",
      adresse: patient.adresse || "",
    });
    setIsDialogOpen(true);
  };

  const handleAddNewClick = () => {
    setEditingId(null);
    form.reset({
      nom: "",
      prenom: "",
      telephone: "",
      email: "",
      adresse: "",
    });
    setIsDialogOpen(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Patients</h1>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddNewClick}>
              <Plus size={16} className="mr-2" />
              Nouveau Patient
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingId ? "Modifier un patient" : "Ajouter un patient"}
              </DialogTitle>
              <DialogDescription>
                Remplissez les informations du patient ci-dessous.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom*</FormLabel>
                        <FormControl>
                          <Input placeholder="Nom du patient" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="prenom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prénom*</FormLabel>
                        <FormControl>
                          <Input placeholder="Prénom du patient" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="telephone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Téléphone*</FormLabel>
                      <FormControl>
                        <Input placeholder="Numéro de téléphone" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Adresse email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="adresse"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Adresse</FormLabel>
                      <FormControl>
                        <Input placeholder="Adresse du patient" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end pt-4">
                  <Button type="submit">
                    {editingId ? "Mettre à jour" : "Enregistrer"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="mb-6 max-w-md">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un patient..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des patients</CardTitle>
          <CardDescription>
            {filteredPatients.length} patient{filteredPatients.length !== 1 ? 's' : ''} enregistré{filteredPatients.length !== 1 ? 's' : ''}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              Chargement des patients...
            </div>
          ) : filteredPatients.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Prénom</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPatients.map((patient) => (
                  <TableRow key={patient.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleViewPatient(patient.id)}>
                    <TableCell className="font-medium">{patient.nom}</TableCell>
                    <TableCell>{patient.prenom}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Phone size={14} className="mr-1.5 text-muted-foreground" />
                        <span>{patient.telephone}</span>
                      </div>
                      {patient.email && (
                        <div className="flex items-center mt-1">
                          <Mail size={14} className="mr-1.5 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">{patient.email}</span>
                        </div>
                      )}
                      {patient.adresse && (
                        <div className="flex items-center mt-1">
                          <MapPin size={14} className="mr-1.5 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">{patient.adresse}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPatient(patient);
                        }}
                      >
                        Modifier
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              Aucun patient trouvé
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
