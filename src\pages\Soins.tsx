
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { createSoin, getSoins, updateSoin } from "@/services/database";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>, Loader2, Plus, Search } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Définir le type pour un soin
type SoinType = {
  id: string;
  nom: string;
  prix: number;
  type: "service" | "produit";
  quantite?: number;
};

// Schéma de validation
const soinSchema = z.object({
  nom: z.string().min(2, { message: "Le nom doit contenir au moins 2 caractères" }),
  prix: z.coerce
    .number()
    .min(0, { message: "Le prix ne peut pas être négatif" })
    .refine((val) => !isNaN(val), { message: "Le prix doit être un nombre" }),
  type: z.enum(["service", "produit"]),
  quantite: z.coerce
    .number()
    .min(0, { message: "La quantité ne peut pas être négative" })
    .optional(),
});

export default function Soins() {
  const [items, setItems] = useState<SoinType[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Charger les soins depuis Supabase
  useEffect(() => {
    const fetchSoins = async () => {
      try {
        setLoading(true);
        const soinsData = await getSoins();

        // Convertir les données Supabase au format SoinType
        const formattedSoins: SoinType[] = soinsData.map(soin => ({
          id: soin.id,
          nom: soin.nom,
          prix: soin.prix,
          type: soin.type as "service" | "produit",
          ...(soin.quantite !== undefined && { quantite: soin.quantite })
        }));

        setItems(formattedSoins);
      } catch (error) {
        console.error("Erreur lors du chargement des soins:", error);
        toast({
          title: "Erreur",
          description: "Impossible de charger les soins. Veuillez réessayer.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSoins();
  }, [toast]);

  // Initialiser le formulaire
  const form = useForm<z.infer<typeof soinSchema>>({
    resolver: zodResolver(soinSchema),
    defaultValues: {
      nom: "",
      prix: 0,
      type: "service",
    },
  });

  // Filtrer les items selon le terme de recherche
  const filteredItems = items.filter(item =>
    item.nom.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filtrer par type
  const services = filteredItems.filter(item => item.type === "service");
  const produits = filteredItems.filter(item => item.type === "produit");

  // Gérer la soumission du formulaire
  const onSubmit = async (values: z.infer<typeof soinSchema>) => {
    try {
      setLoading(true);

      if (editingId) {
        // Mode édition: mettre à jour un élément existant dans Supabase
        const soinData = {
          nom: values.nom,
          prix: values.prix,
          type: values.type,
          ...(values.quantite !== undefined && { quantite: values.quantite }),
        };

        await updateSoin(editingId, soinData);

        // Mettre à jour l'état local
        const updatedItems = items.map(item =>
          item.id === editingId ? { ...item, ...values } : item
        );
        setItems(updatedItems);

        toast({
          title: "Élément mis à jour",
          description: `${values.nom} a été modifié avec succès.`
        });
      } else {
        // Nouveau item: ajouter un nouvel élément dans Supabase
        const soinData = {
          nom: values.nom,
          prix: values.prix,
          type: values.type,
          ...(values.quantite !== undefined && { quantite: values.quantite }),
        };

        const newSoin = await createSoin(soinData);

        if (newSoin) {
          // Mettre à jour l'état local avec le nouvel élément
          const newItem: SoinType = {
            id: newSoin.id,
            nom: newSoin.nom,
            prix: newSoin.prix,
            type: newSoin.type as "service" | "produit",
            ...(newSoin.quantite !== undefined && { quantite: newSoin.quantite }),
          };

          setItems([...items, newItem]);

          toast({
            title: "Élément ajouté",
            description: `${values.nom} a été ajouté avec succès.`
          });
        }
      }
    } catch (error) {
      console.error("Erreur lors de l'enregistrement:", error);
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);

      // Réinitialiser le formulaire et fermer la boîte de dialogue
      form.reset();
      setEditingId(null);
      setIsDialogOpen(false);
    }
  };

  const handleEdit = (item: SoinType) => {
    setEditingId(item.id);
    form.reset({
      nom: item.nom,
      prix: item.prix,
      type: item.type,
      quantite: item.quantite,
    });
    setIsDialogOpen(true);
  };

  const handleAddNewClick = () => {
    setEditingId(null);
    form.reset({
      nom: "",
      prix: 0,
      type: "service",
      quantite: undefined,
    });
    setIsDialogOpen(true);
  };

  // Rendu du tableau d'items
  const renderItemsTable = (items: SoinType[]) => {
    if (items.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          Aucun élément trouvé
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nom</TableHead>
            <TableHead>Prix</TableHead>
            {items[0]?.type === "produit" && <TableHead>Quantité</TableHead>}
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id}>
              <TableCell>{item.nom}</TableCell>
              <TableCell>{item.prix.toFixed(0)} XAF</TableCell>
              {item.type === "produit" && <TableCell>{item.quantite || 0}</TableCell>}
              <TableCell>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEdit(item)}
                >
                  <Edit size={16} />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Soins et Articles</h1>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleAddNewClick}>
              <Plus size={16} className="mr-2" />
              Nouveau
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingId ? "Modifier un élément" : "Ajouter un élément"}
              </DialogTitle>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
                <FormField
                  control={form.control}
                  name="nom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom</FormLabel>
                      <FormControl>
                        <Input placeholder="Nom de l'élément" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="prix"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prix (XAF)</FormLabel>
                        <FormControl>
                          <Input type="number" step="0.01" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner un type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="service">Service</SelectItem>
                            <SelectItem value="produit">Produit</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {form.watch("type") === "produit" && (
                  <FormField
                    control={form.control}
                    name="quantite"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Quantité en stock</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <div className="flex justify-end pt-4">
                  <Button type="submit">
                    {editingId ? "Mettre à jour" : "Enregistrer"}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="mb-6 max-w-md">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Liste des soins et articles</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-muted-foreground">Chargement des soins...</p>
              </div>
            </div>
          ) : (
            <Tabs defaultValue="tous">
              <TabsList className="mb-4">
                <TabsTrigger value="tous">Tous ({filteredItems.length})</TabsTrigger>
                <TabsTrigger value="services">Services ({services.length})</TabsTrigger>
                <TabsTrigger value="produits">Produits ({produits.length})</TabsTrigger>
              </TabsList>

              <TabsContent value="tous">
                {renderItemsTable(filteredItems)}
              </TabsContent>

              <TabsContent value="services">
                {renderItemsTable(services)}
              </TabsContent>

              <TabsContent value="produits">
                {renderItemsTable(produits)}
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
