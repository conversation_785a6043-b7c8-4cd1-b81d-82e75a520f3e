import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { getHospitalsWithStats } from '@/services/hospitalService';
import { getUserStats } from '@/services/userService';
import { HospitalWithStats } from '@/types/app-types';
import { 
  Building2, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Plus,
  Settings,
  Eye,
  UserCheck,
  UserX
} from 'lucide-react';
import { Link } from 'react-router-dom';

export default function SuperAdminDashboard() {
  const [hospitals, setHospitals] = useState<HospitalWithStats[]>([]);
  const [userStats, setUserStats] = useState({
    total: 0,
    superAdmins: 0,
    hospitalAdmins: 0,
    activeHospitals: 0
  });
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [hospitalsData, statsData] = await Promise.all([
        getHospitalsWithStats(),
        getUserStats()
      ]);
      
      setHospitals(hospitalsData);
      setUserStats(statsData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données du tableau de bord.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspendu</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const totalRevenue = hospitals.reduce((sum, hospital) => sum + (hospital.total_revenue || 0), 0);
  const totalPatients = hospitals.reduce((sum, hospital) => sum + (hospital.patient_count || 0), 0);
  const totalFactures = hospitals.reduce((sum, hospital) => sum + (hospital.facture_count || 0), 0);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Dashboard Super-Admin</h1>
          <p className="text-gray-600">Vue d'ensemble de tous les hôpitaux</p>
        </div>
        <Link to="/super-admin/hospitals/new">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Nouvel Hôpital
          </Button>
        </Link>
      </div>

      {/* Statistiques globales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hôpitaux</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{hospitals.length}</div>
            <p className="text-xs text-muted-foreground">
              {userStats.activeHospitals} actifs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPatients.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Tous hôpitaux confondus
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Factures</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFactures.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Factures générées
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chiffre d'Affaires</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRevenue.toLocaleString()} XAF</div>
            <p className="text-xs text-muted-foreground">
              Revenue total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Liste des hôpitaux */}
      <Card>
        <CardHeader>
          <CardTitle>Hôpitaux</CardTitle>
          <CardDescription>
            Gestion et supervision de tous les hôpitaux
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {hospitals.map((hospital) => (
              <div key={hospital.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    {hospital.logo_url ? (
                      <img 
                        src={hospital.logo_url} 
                        alt={hospital.name}
                        className="w-10 h-10 object-cover rounded"
                      />
                    ) : (
                      <Building2 className="h-6 w-6 text-gray-400" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold">{hospital.name}</h3>
                    <p className="text-sm text-gray-600">{hospital.contact_email}</p>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-xs text-gray-500">
                        {hospital.patient_count} patients
                      </span>
                      <span className="text-xs text-gray-500">
                        {hospital.facture_count} factures
                      </span>
                      <span className="text-xs text-gray-500">
                        {(hospital.total_revenue || 0).toLocaleString()} XAF
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(hospital.account_status)}
                  <Link to={`/super-admin/hospitals/${hospital.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      Voir
                    </Button>
                  </Link>
                  <Link to={`/super-admin/hospitals/${hospital.id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-1" />
                      Gérer
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
