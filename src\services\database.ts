
import { supabase } from '@/integrations/supabase/client';
import {
  Facture, FactureInsert,
  FactureItem, FactureItemInsert,
  Patient, PatientInsert,
  Soin, SoinInsert
} from '@/types/app-types';

// Patients
export async function getPatients(): Promise<Patient[]> {
  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching patients:', error);
    return [];
  }

  return data || [];
}

export async function getPatientById(id: string): Promise<Patient | null> {
  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching patient with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function createPatient(patient: PatientInsert): Promise<Patient | null> {
  const { data, error } = await supabase
    .from('patients')
    .insert(patient)
    .select()
    .single();

  if (error) {
    console.error('Error creating patient:', error);
    return null;
  }

  return data;
}

// Soins (Medical Services)
export async function getSoins(): Promise<Soin[]> {
  const { data, error } = await supabase
    .from('soins')
    .select('*')
    .order('nom');

  if (error) {
    console.error('Error fetching soins:', error);
    return [];
  }

  return data || [];
}

export async function createSoin(soin: SoinInsert): Promise<Soin | null> {
  const { data, error } = await supabase
    .from('soins')
    .insert(soin)
    .select()
    .single();

  if (error) {
    console.error('Error creating soin:', error);
    return null;
  }

  return data;
}

export async function updateSoin(id: string, soin: Partial<Soin>): Promise<Soin | null> {
  const { data, error } = await supabase
    .from('soins')
    .update(soin)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating soin with id ${id}:`, error);
    return null;
  }

  return data;
}

// Factures (Invoices)
export async function getFactures(): Promise<Facture[]> {
  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .order('date', { ascending: false });

  if (error) {
    console.error('Error fetching factures:', error);
    return [];
  }

  return data || [];
}

export async function getFactureById(id: string): Promise<Facture | null> {
  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching facture with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function createFacture(facture: FactureInsert): Promise<Facture | null> {
  const { data, error } = await supabase
    .from('factures')
    .insert(facture)
    .select()
    .single();

  if (error) {
    console.error('Error creating facture:', error);
    return null;
  }

  return data;
}

export async function updateFacture(id: string, facture: Partial<Facture>): Promise<Facture | null> {
  const { data, error } = await supabase
    .from('factures')
    .update(facture)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating facture with id ${id}:`, error);
    return null;
  }

  return data;
}

// Facture Items
export async function getFactureItemsByFactureId(factureId: string): Promise<FactureItem[]> {
  const { data, error } = await supabase
    .from('facture_items')
    .select('*')
    .eq('facture_id', factureId);

  if (error) {
    console.error(`Error fetching items for facture ${factureId}:`, error);
    return [];
  }

  return data || [];
}

export async function createFactureItems(items: FactureItemInsert[]): Promise<FactureItem[]> {
  const { data, error } = await supabase
    .from('facture_items')
    .insert(items)
    .select();

  if (error) {
    console.error('Error creating facture items:', error);
    return [];
  }

  return data || [];
}

export async function deleteFactureItemsByFactureId(factureId: string): Promise<void> {
  const { error } = await supabase
    .from('facture_items')
    .delete()
    .eq('facture_id', factureId);

  if (error) {
    console.error(`Error deleting items for facture ${factureId}:`, error);
    throw error;
  }
}



