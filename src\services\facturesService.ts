
import { supabase } from "@/integrations/supabase/client";
import { Facture, FactureInsert, FactureWithDetails } from "@/types/app-types";

export async function getFactures(): Promise<Facture[]> {
  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .order('date', { ascending: false });
    
  if (error) {
    console.error('Error fetching factures:', error);
    throw error;
  }
  
  return data || [];
}

export async function getFacturesByPatient(patientId: string): Promise<Facture[]> {
  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .eq('patient_id', patientId)
    .order('date', { ascending: false });
    
  if (error) {
    console.error(`Error fetching factures for patient ${patientId}:`, error);
    throw error;
  }
  
  return data || [];
}

export async function getFactureWithDetails(factureId: string): Promise<FactureWithDetails | null> {
  const { data, error } = await supabase
    .from('factures')
    .select(`
      *,
      patient:patient_id(*),
      items:facture_items(*)
    `)
    .eq('id', factureId)
    .single();
    
  if (error) {
    console.error(`Error fetching facture ${factureId}:`, error);
    throw error;
  }
  
  return data;
}
