import { supabase } from '@/integrations/supabase/client';
import { Hospital, HospitalInsert, HospitalUpdate, HospitalWithStats } from '@/types/app-types';

// Récupérer tous les hôpitaux (pour super-admin)
export async function getHospitals(): Promise<Hospital[]> {
  const { data, error } = await supabase
    .from('hospitals')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching hospitals:', error);
    throw error;
  }

  return data || [];
}

// Récupérer un hôpital par ID
export async function getHospitalById(id: string): Promise<Hospital | null> {
  const { data, error } = await supabase
    .from('hospitals')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching hospital with id ${id}:`, error);
    return null;
  }

  return data;
}

// Récupérer les hôpitaux avec statistiques
export async function getHospitalsWithStats(): Promise<HospitalWithStats[]> {
  const { data: hospitals, error: hospitalsError } = await supabase
    .from('hospitals')
    .select('*')
    .order('created_at', { ascending: false });

  if (hospitalsError) {
    console.error('Error fetching hospitals:', hospitalsError);
    throw hospitalsError;
  }

  // Récupérer les statistiques pour chaque hôpital
  const hospitalsWithStats = await Promise.all(
    (hospitals || []).map(async (hospital) => {
      const [patientCount, factureStats] = await Promise.all([
        getHospitalPatientCount(hospital.id),
        getHospitalFactureStats(hospital.id)
      ]);

      return {
        ...hospital,
        patient_count: patientCount,
        facture_count: factureStats.count,
        total_revenue: factureStats.revenue
      };
    })
  );

  return hospitalsWithStats;
}

// Compter les patients d'un hôpital
async function getHospitalPatientCount(hospitalId: string): Promise<number> {
  const { count, error } = await supabase
    .from('patients')
    .select('*', { count: 'exact', head: true })
    .eq('hospital_id', hospitalId);

  if (error) {
    console.error(`Error counting patients for hospital ${hospitalId}:`, error);
    return 0;
  }

  return count || 0;
}

// Récupérer les statistiques de factures d'un hôpital
async function getHospitalFactureStats(hospitalId: string): Promise<{ count: number; revenue: number }> {
  const { data, error } = await supabase
    .from('factures')
    .select('montant_total')
    .eq('hospital_id', hospitalId);

  if (error) {
    console.error(`Error fetching facture stats for hospital ${hospitalId}:`, error);
    return { count: 0, revenue: 0 };
  }

  const count = data?.length || 0;
  const revenue = data?.reduce((sum, facture) => sum + facture.montant_total, 0) || 0;

  return { count, revenue };
}

// Créer un nouvel hôpital
export async function createHospital(hospital: HospitalInsert): Promise<Hospital | null> {
  const { data, error } = await supabase
    .from('hospitals')
    .insert(hospital)
    .select()
    .single();

  if (error) {
    console.error('Error creating hospital:', error);
    throw error;
  }

  return data;
}

// Mettre à jour un hôpital
export async function updateHospital(id: string, hospital: HospitalUpdate): Promise<Hospital | null> {
  const { data, error } = await supabase
    .from('hospitals')
    .update(hospital)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating hospital with id ${id}:`, error);
    throw error;
  }

  return data;
}

// Supprimer un hôpital
export async function deleteHospital(id: string): Promise<void> {
  const { error } = await supabase
    .from('hospitals')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting hospital with id ${id}:`, error);
    throw error;
  }
}

// Activer/désactiver un hôpital
export async function toggleHospitalStatus(id: string, status: 'active' | 'inactive'): Promise<Hospital | null> {
  return updateHospital(id, { account_status: status });
}

// Mettre à jour le statut d'abonnement
export async function updateSubscriptionStatus(
  id: string, 
  status: 'active' | 'inactive' | 'suspended'
): Promise<Hospital | null> {
  return updateHospital(id, { subscription_status: status });
}

// Upload du logo de l'hôpital
export async function uploadHospitalLogo(hospitalId: string, file: File): Promise<string | null> {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${hospitalId}-${Date.now()}.${fileExt}`;
    const filePath = `hospital-logos/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('hospital-assets')
      .upload(filePath, file);

    if (uploadError) {
      console.error('Error uploading logo:', uploadError);
      throw uploadError;
    }

    const { data: { publicUrl } } = supabase.storage
      .from('hospital-assets')
      .getPublicUrl(filePath);

    // Mettre à jour l'URL du logo dans la base de données
    await updateHospital(hospitalId, { logo_url: publicUrl });

    return publicUrl;
  } catch (error) {
    console.error('Error uploading hospital logo:', error);
    throw error;
  }
}
