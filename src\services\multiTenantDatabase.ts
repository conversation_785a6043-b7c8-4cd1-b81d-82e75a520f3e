import { supabase } from '@/integrations/supabase/client';
import {
  Facture, FactureInsert,
  FactureItem, FactureItemInsert,
  Patient, PatientInsert,
  Soin, SoinInsert
} from '@/types/app-types';

// Helper function to get current user's hospital_id
async function getCurrentUserHospitalId(): Promise<string | null> {
  const { data: { session } } = await supabase.auth.getSession();
  if (!session?.user?.email) return null;

  const { data: userData } = await supabase
    .from('users')
    .select('hospital_id')
    .eq('email', session.user.email)
    .single();

  return userData?.hospital_id || null;
}

// Patients with multi-tenant filtering
export async function getPatientsByHospital(hospitalId?: string): Promise<Patient[]> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return [];

  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .eq('hospital_id', targetHospitalId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching patients:', error);
    return [];
  }

  return data || [];
}

export async function getPatientByIdAndHospital(id: string, hospitalId?: string): Promise<Patient | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return null;

  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .eq('id', id)
    .eq('hospital_id', targetHospitalId)
    .single();

  if (error) {
    console.error(`Error fetching patient with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function createPatientForHospital(patient: Omit<PatientInsert, 'hospital_id'>, hospitalId?: string): Promise<Patient | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) {
    throw new Error('Hospital ID is required');
  }

  const patientData: PatientInsert = {
    ...patient,
    hospital_id: targetHospitalId
  };

  const { data, error } = await supabase
    .from('patients')
    .insert(patientData)
    .select()
    .single();

  if (error) {
    console.error('Error creating patient:', error);
    throw error;
  }

  return data;
}

export async function updatePatientForHospital(id: string, patient: Partial<Omit<Patient, 'hospital_id'>>, hospitalId?: string): Promise<Patient | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return null;

  const { data, error } = await supabase
    .from('patients')
    .update(patient)
    .eq('id', id)
    .eq('hospital_id', targetHospitalId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating patient with id ${id}:`, error);
    throw error;
  }

  return data;
}

// Soins with multi-tenant filtering
export async function getSoinsByHospital(hospitalId?: string): Promise<Soin[]> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return [];

  const { data, error } = await supabase
    .from('soins')
    .select('*')
    .eq('hospital_id', targetHospitalId)
    .order('nom');

  if (error) {
    console.error('Error fetching soins:', error);
    return [];
  }

  return data || [];
}

export async function createSoinForHospital(soin: Omit<SoinInsert, 'hospital_id'>, hospitalId?: string): Promise<Soin | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) {
    throw new Error('Hospital ID is required');
  }

  const soinData: SoinInsert = {
    ...soin,
    hospital_id: targetHospitalId
  };

  const { data, error } = await supabase
    .from('soins')
    .insert(soinData)
    .select()
    .single();

  if (error) {
    console.error('Error creating soin:', error);
    throw error;
  }

  return data;
}

export async function updateSoinForHospital(id: string, soin: Partial<Omit<Soin, 'hospital_id'>>, hospitalId?: string): Promise<Soin | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return null;

  const { data, error } = await supabase
    .from('soins')
    .update(soin)
    .eq('id', id)
    .eq('hospital_id', targetHospitalId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating soin with id ${id}:`, error);
    throw error;
  }

  return data;
}

// Factures with multi-tenant filtering
export async function getFacturesByHospital(hospitalId?: string): Promise<Facture[]> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return [];

  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .eq('hospital_id', targetHospitalId)
    .order('date', { ascending: false });

  if (error) {
    console.error('Error fetching factures:', error);
    return [];
  }

  return data || [];
}

export async function getFactureByIdAndHospital(id: string, hospitalId?: string): Promise<Facture | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return null;

  const { data, error } = await supabase
    .from('factures')
    .select('*')
    .eq('id', id)
    .eq('hospital_id', targetHospitalId)
    .single();

  if (error) {
    console.error(`Error fetching facture with id ${id}:`, error);
    return null;
  }

  return data;
}

export async function createFactureForHospital(facture: Omit<FactureInsert, 'hospital_id'>, hospitalId?: string): Promise<Facture | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) {
    throw new Error('Hospital ID is required');
  }

  const factureData: FactureInsert = {
    ...facture,
    hospital_id: targetHospitalId
  };

  const { data, error } = await supabase
    .from('factures')
    .insert(factureData)
    .select()
    .single();

  if (error) {
    console.error('Error creating facture:', error);
    throw error;
  }

  return data;
}

export async function updateFactureForHospital(id: string, facture: Partial<Omit<Facture, 'hospital_id'>>, hospitalId?: string): Promise<Facture | null> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return null;

  const { data, error } = await supabase
    .from('factures')
    .update(facture)
    .eq('id', id)
    .eq('hospital_id', targetHospitalId)
    .select()
    .single();

  if (error) {
    console.error(`Error updating facture with id ${id}:`, error);
    throw error;
  }

  return data;
}

// Facture Items with multi-tenant filtering
export async function getFactureItemsByFactureIdAndHospital(factureId: string, hospitalId?: string): Promise<FactureItem[]> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return [];

  const { data, error } = await supabase
    .from('facture_items')
    .select('*')
    .eq('facture_id', factureId)
    .eq('hospital_id', targetHospitalId);

  if (error) {
    console.error(`Error fetching items for facture ${factureId}:`, error);
    return [];
  }

  return data || [];
}

export async function createFactureItemsForHospital(items: Omit<FactureItemInsert, 'hospital_id'>[], hospitalId?: string): Promise<FactureItem[]> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) {
    throw new Error('Hospital ID is required');
  }

  const itemsData: FactureItemInsert[] = items.map(item => ({
    ...item,
    hospital_id: targetHospitalId
  }));

  const { data, error } = await supabase
    .from('facture_items')
    .insert(itemsData)
    .select();

  if (error) {
    console.error('Error creating facture items:', error);
    throw error;
  }

  return data || [];
}

export async function deleteFactureItemsByFactureIdAndHospital(factureId: string, hospitalId?: string): Promise<void> {
  const targetHospitalId = hospitalId || await getCurrentUserHospitalId();
  if (!targetHospitalId) return;

  const { error } = await supabase
    .from('facture_items')
    .delete()
    .eq('facture_id', factureId)
    .eq('hospital_id', targetHospitalId);

  if (error) {
    console.error(`Error deleting items for facture ${factureId}:`, error);
    throw error;
  }
}
