
import { supabase } from "@/integrations/supabase/client";
import { Patient, PatientInsert } from "@/types/app-types";

export async function getPatients(): Promise<Patient[]> {
  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .order('nom');
    
  if (error) {
    console.error('Error fetching patients:', error);
    throw error;
  }
  
  return data || [];
}

export async function getPatientById(id: string): Promise<Patient> {
  const { data, error } = await supabase
    .from('patients')
    .select('*')
    .eq('id', id)
    .single();
    
  if (error) {
    console.error(`Error fetching patient ${id}:`, error);
    throw error;
  }
  
  return data;
}

export async function createPatient(patient: PatientInsert): Promise<Patient> {
  const { data, error } = await supabase
    .from('patients')
    .insert(patient)
    .select()
    .single();
    
  if (error) {
    console.error('Error creating patient:', error);
    throw error;
  }
  
  return data;
}

export async function updatePatient(id: string, patient: Partial<Patient>): Promise<Patient> {
  const { data, error } = await supabase
    .from('patients')
    .update(patient)
    .eq('id', id)
    .select()
    .single();
    
  if (error) {
    console.error('Error updating patient:', error);
    throw error;
  }
  
  return data;
}
