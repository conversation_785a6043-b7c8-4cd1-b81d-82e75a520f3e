import { supabase } from '@/integrations/supabase/client';
import { User, UserInsert, UserUpdate, UserWithHospital } from '@/types/app-types';

// Récupérer tous les utilisateurs (pour super-admin)
export async function getUsers(): Promise<UserWithHospital[]> {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      hospital:hospitals(*)
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching users:', error);
    throw error;
  }

  return data || [];
}

// Récupérer un utilisateur par ID
export async function getUserById(id: string): Promise<UserWithHospital | null> {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      hospital:hospitals(*)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching user with id ${id}:`, error);
    return null;
  }

  return data;
}

// Récupérer un utilisateur par email
export async function getUserByEmail(email: string): Promise<UserWithHospital | null> {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      hospital:hospitals(*)
    `)
    .eq('email', email)
    .single();

  if (error) {
    console.error(`Error fetching user with email ${email}:`, error);
    return null;
  }

  return data;
}

// Récupérer les utilisateurs d'un hôpital
export async function getUsersByHospital(hospitalId: string): Promise<UserWithHospital[]> {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      hospital:hospitals(*)
    `)
    .eq('hospital_id', hospitalId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`Error fetching users for hospital ${hospitalId}:`, error);
    throw error;
  }

  return data || [];
}

// Créer un nouvel utilisateur
export async function createUser(user: UserInsert): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .insert(user)
    .select()
    .single();

  if (error) {
    console.error('Error creating user:', error);
    throw error;
  }

  return data;
}

// Mettre à jour un utilisateur
export async function updateUser(id: string, user: UserUpdate): Promise<User | null> {
  const { data, error } = await supabase
    .from('users')
    .update(user)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating user with id ${id}:`, error);
    throw error;
  }

  return data;
}

// Supprimer un utilisateur
export async function deleteUser(id: string): Promise<void> {
  const { error } = await supabase
    .from('users')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting user with id ${id}:`, error);
    throw error;
  }
}

// Créer un utilisateur hospital-admin pour un hôpital
export async function createHospitalAdmin(
  email: string,
  fullName: string,
  hospitalId: string
): Promise<User | null> {
  const userData: UserInsert = {
    email,
    full_name: fullName,
    role: 'hospital_admin',
    hospital_id: hospitalId
  };

  return createUser(userData);
}

// Créer un utilisateur super-admin
export async function createSuperAdmin(
  email: string,
  fullName: string
): Promise<User | null> {
  const userData: UserInsert = {
    email,
    full_name: fullName,
    role: 'super_admin'
  };

  return createUser(userData);
}

// Vérifier si un email existe déjà
export async function checkEmailExists(email: string): Promise<boolean> {
  const { data, error } = await supabase
    .from('users')
    .select('id')
    .eq('email', email)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    console.error('Error checking email:', error);
    throw error;
  }

  return !!data;
}

// Récupérer les statistiques des utilisateurs
export async function getUserStats(): Promise<{
  total: number;
  superAdmins: number;
  hospitalAdmins: number;
  activeHospitals: number;
}> {
  const [usersData, hospitalsData] = await Promise.all([
    supabase.from('users').select('role'),
    supabase.from('hospitals').select('account_status').eq('account_status', 'active')
  ]);

  if (usersData.error) {
    console.error('Error fetching user stats:', usersData.error);
    throw usersData.error;
  }

  if (hospitalsData.error) {
    console.error('Error fetching hospital stats:', hospitalsData.error);
    throw hospitalsData.error;
  }

  const users = usersData.data || [];
  const activeHospitals = hospitalsData.data?.length || 0;

  return {
    total: users.length,
    superAdmins: users.filter(u => u.role === 'super_admin').length,
    hospitalAdmins: users.filter(u => u.role === 'hospital_admin').length,
    activeHospitals
  };
}
