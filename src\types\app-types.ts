
// Application-specific types that work with Supabase
import type { Database } from '@/integrations/supabase/types';

// Supabase tables as types
export type Hospital = Database['public']['Tables']['hospitals']['Row'];
export type HospitalInsert = Database['public']['Tables']['hospitals']['Insert'];
export type HospitalUpdate = Database['public']['Tables']['hospitals']['Update'];
export type User = Database['public']['Tables']['users']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];
export type Patient = Database['public']['Tables']['patients']['Row'];
export type PatientInsert = Database['public']['Tables']['patients']['Insert'];
export type Soin = Database['public']['Tables']['soins']['Row'];
export type SoinInsert = Database['public']['Tables']['soins']['Insert'];
export type Facture = Database['public']['Tables']['factures']['Row'];
export type FactureInsert = Database['public']['Tables']['factures']['Insert'];
export type FactureItem = Database['public']['Tables']['facture_items']['Row'];
export type FactureItemInsert = Database['public']['Tables']['facture_items']['Insert'];

// Additional application-specific types
export type FactureStatus = 'payée' | 'avance' | 'non payée';
export type SoinType = 'service' | 'produit';
export type UserRole = 'super_admin' | 'hospital_admin';
export type SubscriptionStatus = 'active' | 'inactive' | 'suspended';
export type AccountStatus = 'active' | 'inactive';

// Extended types with relationships
export interface FactureWithDetails extends Facture {
  patient?: Patient;
  items?: FactureItem[];
  hospital?: Hospital;
}

export interface FactureItemWithDetails extends FactureItem {
  soin?: Soin;
}

export interface HospitalWithStats extends Hospital {
  patient_count?: number;
  facture_count?: number;
  total_revenue?: number;
}

export interface UserWithHospital extends User {
  hospital?: Hospital;
}

// Authentication context types
export interface AuthUser {
  id: string;
  email: string;
  role: UserRole;
  hospital_id?: string;
  full_name?: string;
  hospital?: Hospital;
}

export interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  isSuperAdmin: boolean;
  isHospitalAdmin: boolean;
}
