-- Migration pour implémenter le système multi-tenant
-- À exécuter dans l'interface Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON> la table hospitals
CREATE TABLE IF NOT EXISTS public.hospitals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  logo_url TEXT,
  contact_email VARCHAR(255),
  contact_phone VARCHAR(50),
  address TEXT,
  subscription_status VARCHAR(20) DEFAULT 'active' CHECK (subscription_status IN ('active', 'inactive', 'suspended')),
  account_status VARCHAR(20) DEFAULT 'active' CHECK (account_status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON>er la table users pour l'authentification
CREATE TABLE IF NOT EXISTS public.users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  role VARCHAR(20) NOT NULL CHECK (role IN ('super_admin', 'hospital_admin')),
  hospital_id UUID REFERENCES public.hospitals(id) ON DELETE CASCADE,
  full_name VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Ajouter la colonne hospital_id aux tables existantes
ALTER TABLE public.patients ADD COLUMN IF NOT EXISTS hospital_id UUID REFERENCES public.hospitals(id) ON DELETE CASCADE;
ALTER TABLE public.soins ADD COLUMN IF NOT EXISTS hospital_id UUID REFERENCES public.hospitals(id) ON DELETE CASCADE;
ALTER TABLE public.factures ADD COLUMN IF NOT EXISTS hospital_id UUID REFERENCES public.hospitals(id) ON DELETE CASCADE;
ALTER TABLE public.facture_items ADD COLUMN IF NOT EXISTS hospital_id UUID REFERENCES public.hospitals(id) ON DELETE CASCADE;

-- 4. Créer un hôpital par défaut pour les données existantes
INSERT INTO public.hospitals (id, name, subscription_status, account_status)
VALUES ('********-0000-0000-0000-************', 'Hôpital par défaut', 'active', 'active')
ON CONFLICT (id) DO NOTHING;

-- 5. Mettre à jour les données existantes avec l'hôpital par défaut
UPDATE public.patients SET hospital_id = '********-0000-0000-0000-************' WHERE hospital_id IS NULL;
UPDATE public.soins SET hospital_id = '********-0000-0000-0000-************' WHERE hospital_id IS NULL;
UPDATE public.factures SET hospital_id = '********-0000-0000-0000-************' WHERE hospital_id IS NULL;
UPDATE public.facture_items SET hospital_id = '********-0000-0000-0000-************' WHERE hospital_id IS NULL;

-- 6. Rendre les colonnes hospital_id obligatoires
ALTER TABLE public.patients ALTER COLUMN hospital_id SET NOT NULL;
ALTER TABLE public.soins ALTER COLUMN hospital_id SET NOT NULL;
ALTER TABLE public.factures ALTER COLUMN hospital_id SET NOT NULL;
ALTER TABLE public.facture_items ALTER COLUMN hospital_id SET NOT NULL;

-- 7. Créer un utilisateur super-admin par défaut
INSERT INTO public.users (id, email, role, full_name)
VALUES ('********-0000-0000-0000-************', '<EMAIL>', 'super_admin', 'Super Administrateur')
ON CONFLICT (email) DO NOTHING;

-- 8. Créer un trigger pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_hospitals_updated_at BEFORE UPDATE ON public.hospitals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
