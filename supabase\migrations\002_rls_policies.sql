-- Politiques RLS (Row Level Security) pour l'isolation multi-tenant
-- À exécuter dans l'interface Supabase SQL Editor après 001_multi_tenant_setup.sql

-- 1. Activer RLS sur toutes les tables
ALTER TABLE public.hospitals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.soins ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.factures ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.facture_items ENABLE ROW LEVEL SECURITY;

-- 2. Politiques pour la table hospitals
-- Super-admins peuvent tout voir et modifier
CREATE POLICY "Super admins can view all hospitals" ON public.hospitals
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

CREATE POLICY "Super admins can insert hospitals" ON public.hospitals
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

CREATE POLICY "Super admins can update hospitals" ON public.hospitals
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

CREATE POLICY "Super admins can delete hospitals" ON public.hospitals
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

-- Hospital admins peuvent voir leur propre hôpital
CREATE POLICY "Hospital admins can view their hospital" ON public.hospitals
  FOR SELECT USING (
    id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

-- 3. Politiques pour la table users
-- Super-admins peuvent tout voir et modifier
CREATE POLICY "Super admins can manage all users" ON public.users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

-- Hospital admins peuvent voir les utilisateurs de leur hôpital
CREATE POLICY "Hospital admins can view their hospital users" ON public.users
  FOR SELECT USING (
    hospital_id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

-- 4. Politiques pour la table patients
-- Hospital admins peuvent gérer les patients de leur hôpital
CREATE POLICY "Hospital admins can manage their patients" ON public.patients
  FOR ALL USING (
    hospital_id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

-- Super-admins peuvent tout voir
CREATE POLICY "Super admins can view all patients" ON public.patients
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

-- 5. Politiques pour la table soins
CREATE POLICY "Hospital admins can manage their soins" ON public.soins
  FOR ALL USING (
    hospital_id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

CREATE POLICY "Super admins can view all soins" ON public.soins
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

-- 6. Politiques pour la table factures
CREATE POLICY "Hospital admins can manage their factures" ON public.factures
  FOR ALL USING (
    hospital_id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

CREATE POLICY "Super admins can view all factures" ON public.factures
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );

-- 7. Politiques pour la table facture_items
CREATE POLICY "Hospital admins can manage their facture_items" ON public.facture_items
  FOR ALL USING (
    hospital_id IN (
      SELECT hospital_id FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'hospital_admin'
    )
  );

CREATE POLICY "Super admins can view all facture_items" ON public.facture_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE users.email = auth.jwt() ->> 'email' 
      AND users.role = 'super_admin'
    )
  );
