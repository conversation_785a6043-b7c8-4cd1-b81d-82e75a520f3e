#!/usr/bin/env node

/**
 * Test login functionality to debug login issues
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9zaGxocmdhenRmYmlodXhlb3B1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0Mzc1ODIsImV4cCI6MjA2MjAxMzU4Mn0.ouZSe00DWjc0bSjO7FfLwOdoIlgDAyD7HErUjYjwB8o";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'BetterCare2024!'
  },
  {
    email: '<EMAIL>',
    password: 'Hospital2024!'
  }
];

async function testLogin(email, password) {
  console.log(`\n🔐 Testing login for: ${email}`);
  
  try {
    // Step 1: Test authentication
    console.log('  Step 1: Attempting authentication...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      console.log(`  ❌ Authentication failed: ${authError.message}`);
      return false;
    }

    console.log('  ✅ Authentication successful');
    console.log(`  📧 User ID: ${authData.user?.id}`);
    console.log(`  📧 Email: ${authData.user?.email}`);

    // Step 2: Test user data loading (same as AuthContext)
    console.log('  Step 2: Loading user data...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (userError) {
      console.log(`  ❌ User data loading failed: ${userError.message}`);
      console.log(`  📋 Error details:`, userError);
      return false;
    }

    console.log('  ✅ User data loaded successfully');
    console.log(`  👤 Role: ${userData.role}`);
    console.log(`  🏥 Hospital ID: ${userData.hospital_id || 'None (Super Admin)'}`);

    // Step 3: Test hospital data loading (if applicable)
    if (userData.hospital_id) {
      console.log('  Step 3: Loading hospital data...');
      const { data: hospitalData, error: hospitalError } = await supabase
        .from('hospitals')
        .select('*')
        .eq('id', userData.hospital_id)
        .single();

      if (hospitalError) {
        console.log(`  ❌ Hospital data loading failed: ${hospitalError.message}`);
        return false;
      }

      console.log('  ✅ Hospital data loaded successfully');
      console.log(`  🏥 Hospital: ${hospitalData.name}`);
    } else {
      console.log('  Step 3: No hospital data needed (Super Admin)');
    }

    // Step 4: Sign out to clean up
    await supabase.auth.signOut();
    console.log('  ✅ Signed out successfully');

    return true;

  } catch (error) {
    console.log(`  ❌ Unexpected error: ${error.message}`);
    console.log(`  📋 Error details:`, error);
    return false;
  }
}

async function checkDatabaseAccess() {
  console.log('\n🗄️  Testing database access...');
  
  try {
    // Test users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('email, role')
      .limit(5);

    if (usersError) {
      console.log(`❌ Users table access failed: ${usersError.message}`);
    } else {
      console.log(`✅ Users table accessible (${users.length} records)`);
    }

    // Test hospitals table
    const { data: hospitals, error: hospitalsError } = await supabase
      .from('hospitals')
      .select('name')
      .limit(5);

    if (hospitalsError) {
      console.log(`❌ Hospitals table access failed: ${hospitalsError.message}`);
    } else {
      console.log(`✅ Hospitals table accessible (${hospitals.length} records)`);
    }

  } catch (error) {
    console.log(`❌ Database access test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🧪 Better Care Login Test');
  console.log('=' * 40);

  // Test database access first
  await checkDatabaseAccess();

  // Test each user login
  let successCount = 0;
  for (const user of TEST_USERS) {
    const success = await testLogin(user.email, user.password);
    if (success) successCount++;
  }

  console.log('\n📊 Test Results:');
  console.log(`✅ Successful logins: ${successCount}/${TEST_USERS.length}`);

  if (successCount === TEST_USERS.length) {
    console.log('\n🎉 All login tests passed!');
    console.log('\nIf you still can\'t login through the web interface:');
    console.log('1. Clear browser storage (F12 → Application → Clear Storage)');
    console.log('2. Hard refresh the page (Ctrl+Shift+R)');
    console.log('3. Check browser console for errors');
    console.log('4. Make sure you\'re using the exact credentials:');
    TEST_USERS.forEach(user => {
      console.log(`   📧 ${user.email}`);
      console.log(`   🔑 ${user.password}`);
    });
  } else {
    console.log('\n❌ Some login tests failed. Check the errors above.');
  }
}

main().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
