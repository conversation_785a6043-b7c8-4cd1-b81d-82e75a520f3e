#!/usr/bin/env node

/**
 * Quick script to update Supabase API keys
 * Usage: node update-keys.js YOUR_ANON_KEY
 */

import fs from 'fs';

const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('\n🔑 Supabase API Key Updater');
  console.log('=' * 30);
  console.log('\nUsage: node update-keys.js YOUR_ANON_KEY');
  console.log('\nTo get your anon key:');
  console.log('1. Go to https://supabase.com/dashboard');
  console.log('2. Select your project: vxzpeinztvurtncaaqdm');
  console.log('3. Go to Settings → API');
  console.log('4. Copy the "anon" or "public" key');
  console.log('\nExample:');
  console.log('node update-keys.js eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
  process.exit(1);
}

const anonKey = args[0];

if (!anonKey.startsWith('eyJ')) {
  console.error('❌ Invalid key format. The anon key should start with "eyJ"');
  process.exit(1);
}

try {
  // Update client.ts
  const clientPath = 'src/integrations/supabase/client.ts';
  const clientContent = `
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vxzpeinztvurtncaaqdm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "${anonKey}";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
`.trim();

  fs.writeFileSync(clientPath, clientContent + '\n');
  console.log('✅ Updated src/integrations/supabase/client.ts');

  // Update verify-setup.js
  try {
    const verifyPath = 'verify-setup.js';
    let verifyContent = fs.readFileSync(verifyPath, 'utf8');
    verifyContent = verifyContent.replace(
      /const SUPABASE_ANON_KEY = ".*";/,
      `const SUPABASE_ANON_KEY = "${anonKey}";`
    );
    fs.writeFileSync(verifyPath, verifyContent);
    console.log('✅ Updated verify-setup.js');
  } catch (error) {
    console.log('⚠️  Could not update verify-setup.js');
  }

  console.log('\n🎉 API keys updated successfully!');
  console.log('\nNext steps:');
  console.log('1. Restart your dev server: npm run dev');
  console.log('2. Clear browser storage (F12 → Application → Clear Storage)');
  console.log('3. Try logging in again');
  console.log('\nIf you need to run setup:');
  console.log('export SUPABASE_SERVICE_KEY="your_service_key"');
  console.log('npm run setup');

} catch (error) {
  console.error('❌ Failed to update files:', error.message);
  process.exit(1);
}
