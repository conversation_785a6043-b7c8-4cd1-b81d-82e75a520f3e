#!/usr/bin/env node

/**
 * Better Care Hospital Management - Setup Verification Script
 * 
 * This script verifies that the setup was completed successfully
 */

import { createClient } from '@supabase/supabase-js';

// Configuration
const SUPABASE_URL = "https://oshlhrgaztfbihuxeopu.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9zaGxocmdhenRmYmlodXhlb3B1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0Mzc1ODIsImV4cCI6MjA2MjAxMzU4Mn0.ouZSe00DWjc0bSjO7FfLwOdoIlgDAyD7HErUjYjwB8o";

// Test credentials
const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'BetterCare2024!',
    expectedRole: 'super_admin'
  },
  {
    email: '<EMAIL>',
    password: 'Hospital2024!',
    expectedRole: 'hospital_admin'
  }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

class SetupVerifier {
  constructor() {
    this.supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    this.results = {
      database: false,
      authentication: false,
      users: false,
      overall: false
    };
  }

  async verify() {
    log('\n🔍 Better Care Setup Verification', 'magenta');
    log('=' * 50, 'magenta');

    await this.verifyDatabase();
    await this.verifyAuthentication();
    await this.verifyUsers();

    this.showResults();
  }

  async verifyDatabase() {
    log('\n📊 Verifying Database Schema...', 'cyan');

    const requiredTables = ['hospitals', 'users', 'patients', 'soins', 'factures', 'facture_items'];
    let tablesExist = 0;

    for (const table of requiredTables) {
      try {
        const { data, error } = await this.supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          logError(`Table '${table}' not accessible: ${error.message}`);
        } else {
          logSuccess(`Table '${table}' exists and accessible`);
          tablesExist++;
        }
      } catch (error) {
        logError(`Error checking table '${table}': ${error.message}`);
      }
    }

    // Check for default hospital
    try {
      const { data: hospitals, error } = await this.supabase
        .from('hospitals')
        .select('*');

      if (error) {
        logError(`Cannot access hospitals table: ${error.message}`);
      } else if (hospitals.length === 0) {
        logWarning('No hospitals found in database');
      } else {
        logSuccess(`Found ${hospitals.length} hospital(s) in database`);
      }
    } catch (error) {
      logError(`Error checking hospitals: ${error.message}`);
    }

    this.results.database = tablesExist === requiredTables.length;

    if (this.results.database) {
      logSuccess('Database schema verification passed');
    } else {
      logError(`Database schema verification failed (${tablesExist}/${requiredTables.length} tables accessible)`);
    }
  }

  async verifyAuthentication() {
    log('\n🔐 Verifying Authentication...', 'cyan');

    let successfulLogins = 0;

    for (const user of TEST_USERS) {
      try {
        logInfo(`Testing login for ${user.email}...`);

        const { data, error } = await this.supabase.auth.signInWithPassword({
          email: user.email,
          password: user.password
        });

        if (error) {
          logError(`Login failed for ${user.email}: ${error.message}`);
        } else {
          logSuccess(`Login successful for ${user.email}`);
          successfulLogins++;

          // Sign out to clean up
          await this.supabase.auth.signOut();
        }
      } catch (error) {
        logError(`Authentication error for ${user.email}: ${error.message}`);
      }
    }

    this.results.authentication = successfulLogins === TEST_USERS.length;

    if (this.results.authentication) {
      logSuccess('Authentication verification passed');
    } else {
      logError(`Authentication verification failed (${successfulLogins}/${TEST_USERS.length} logins successful)`);
    }
  }

  async verifyUsers() {
    log('\n👥 Verifying User Data...', 'cyan');

    try {
      const { data: users, error } = await this.supabase
        .from('users')
        .select('*');

      if (error) {
        logError(`Cannot access users table: ${error.message}`);
        this.results.users = false;
        return;
      }

      logInfo(`Found ${users.length} user(s) in public.users table`);

      let correctUsers = 0;
      for (const testUser of TEST_USERS) {
        const dbUser = users.find(u => u.email === testUser.email);
        if (dbUser) {
          if (dbUser.role === testUser.expectedRole) {
            logSuccess(`User ${testUser.email} has correct role: ${dbUser.role}`);
            correctUsers++;
          } else {
            logError(`User ${testUser.email} has incorrect role: ${dbUser.role} (expected: ${testUser.expectedRole})`);
          }
        } else {
          logError(`User ${testUser.email} not found in public.users table`);
        }
      }

      this.results.users = correctUsers === TEST_USERS.length;

      if (this.results.users) {
        logSuccess('User data verification passed');
      } else {
        logError(`User data verification failed (${correctUsers}/${TEST_USERS.length} users correct)`);
      }

    } catch (error) {
      logError(`Error verifying users: ${error.message}`);
      this.results.users = false;
    }
  }

  showResults() {
    log('\n📋 Verification Results', 'magenta');
    log('=' * 30, 'magenta');

    const checks = [
      { name: 'Database Schema', passed: this.results.database },
      { name: 'Authentication', passed: this.results.authentication },
      { name: 'User Data', passed: this.results.users }
    ];

    checks.forEach(check => {
      if (check.passed) {
        logSuccess(`${check.name}: PASSED`);
      } else {
        logError(`${check.name}: FAILED`);
      }
    });

    this.results.overall = checks.every(check => check.passed);

    log('\n' + '=' * 30, 'magenta');
    if (this.results.overall) {
      logSuccess('🎉 ALL VERIFICATIONS PASSED!');
      log('\nYour Better Care application is ready to use!', 'green');
      log('\nTest Accounts:', 'cyan');
      TEST_USERS.forEach(user => {
        log(`  📧 ${user.email}`, 'blue');
        log(`  🔑 ${user.password}`, 'blue');
        log(`  👤 Role: ${user.expectedRole}`, 'blue');
        log('');
      });
    } else {
      logError('❌ SOME VERIFICATIONS FAILED');
      log('\nPlease run the setup script again:', 'yellow');
      log('  SUPABASE_SERVICE_KEY=your_key npm run setup', 'yellow');
    }
  }
}

// Run the verification
const verifier = new SetupVerifier();
verifier.verify().catch(error => {
  logError(`Verification failed: ${error.message}`);
  process.exit(1);
});
